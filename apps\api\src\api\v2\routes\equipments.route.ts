import { handleEffectError } from '@/api/v2/utils/error-handler';
import { EquipmentsRuntime } from '@/infrastructure/runtimes/equipments.runtime';
import { EquipmentsServiceLive } from '@/infrastructure/services/equipments.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateEquipmentSchema,
    UpdateEquipmentSchema,
    EquipmentSchema,
    ResourceIdSchema
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllEquipmentsRoute = describeRoute({
    description: 'Get all Equipments',
    operationId: 'getAllEquipments',
    responses: {
        200: {
            description: 'List of equipments',
            content: { 'application/json': { schema: resolver(Schema.Array(EquipmentSchema)) } },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Equipments'],
});

export const getEquipmentByIdRoute = describeRoute({
    description: 'Get an Equipment by ID',
    operationId: 'getEquipmentById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Equipment ID (CUID format)',
            example: 'equip123abc456def789ghi'
        },
    ],
    responses: {
        200: {
            description: 'Equipment found',
            content: { 'application/json': { schema: resolver(EquipmentSchema) } },
        },
        404: {
            description: 'Equipment not found',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ error: Schema.String })),
                },
            },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Equipments'],
});

export const createEquipmentRoute = describeRoute({
    description: 'Create an Equipment',
    operationId: 'createEquipment',
    requestBody: {
        required: true,
        content: {
            'application/json': { 
                schema: resolver(CreateEquipmentSchema),
                example: {
                    model: "Advanced Microscope X1000",
                    serialNumber: "AM-X1000-2024-001",
                    typeId: "equipment_type_001",
                    useInClinicalTrial: false,
                    isHidden: false,
                    workingPercentage: 95.5,
                    monetaryCost: 150000.00,
                    manufactureYear: 2024,
                    acquisitionDate: "2024-01-15",
                    installationDate: "2024-02-01",
                    scientificManagerId: "w4ku5n46mdvkv5bjuuml2j3w",
                    manufacturerId: "wjbwu31n6ifz8ijm7g7v2b2m",
                    institutionId: "sydsapwdh5p2m9z9tvnde2dp",
                    isFeatured: false,
                    translations: [
                        {
                            locale: "en",
                            name: "Advanced Research Microscope",
                            description: "High-resolution microscope for advanced research applications",
                            otherNames: "ARM",
                            acronyms: "ARM"
                        },
                        {
                            locale: "fr",
                            name: "Microscope de Recherche Avancé",
                            description: "Microscope haute résolution pour applications de recherche avancées",
                            otherNames: "MRA",
                            acronyms: "MRA"
                        }
                    ]
                }
            },
        },
    },
    responses: {
        201: {
            description: 'Equipment created',
            content: { 'application/json': { schema: resolver(EquipmentSchema) } },
        },
        400: { description: 'Validation error - Invalid input data or missing required fields' },
        404: { description: 'Foreign key not found - Type, Manager, Manufacturer, or Institution does not exist' },
        500: { description: 'Internal server error' },
    },
    tags: ['Equipments'],
});

export const updateEquipmentRoute = describeRoute({
    description: 'Update an Equipment',
    operationId: 'updateEquipment',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Equipment ID (CUID format)',
            example: 'equip123abc456def789ghi'
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': { 
                schema: resolver(UpdateEquipmentSchema),
                example: {
                    model: "Updated Advanced Microscope X2000",
                    serialNumber: "AM-X2000-2024-001",
                    typeId: "equipment_type_002",
                    workingPercentage: 98.0,
                    monetaryCost: 175000.00,
                    manufactureYear: 2024,
                    acquisitionDate: "2024-03-15",
                    installationDate: "2024-04-01",
                    scientificManagerId: "w4ku5n46mdvkv5bjuuml2j3w",
                    manufacturerId: "wjbwu31n6ifz8ijm7g7v2b2m",
                    institutionId: "sydsapwdh5p2m9z9tvnde2dp",
                    isFeatured: true,
                    translations: [
                        {
                            locale: "en",
                            name: "Updated Advanced Research Microscope",
                            description: "Updated high-resolution microscope for advanced research",
                            otherNames: "Updated ARM",
                            acronyms: "UARM"
                        },
                        {
                            locale: "fr",
                            name: "Microscope de Recherche Avancé Mis à Jour",
                            description: "Microscope haute résolution mis à jour pour la recherche avancée",
                            otherNames: "MRA Mis à Jour",
                            acronyms: "MRAMAJ"
                        }
                    ]
                }
            },
        },
    },
    responses: {
        200: {
            description: 'Equipment updated',
            content: { 'application/json': { schema: resolver(EquipmentSchema) } },
        },
        400: { description: 'Validation error - Invalid input data' },
        404: { description: 'Equipment not found or Foreign key not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Equipments'],
});

export const deleteEquipmentRoute = describeRoute({
    description: 'Delete an Equipment',
    operationId: 'deleteEquipment',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Equipment ID (CUID format)',
            example: 'equip123abc456def789ghi'
        },
    ],
    responses: {
        200: {
            description: 'Equipment deleted',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ success: Schema.Boolean, message: Schema.String })),
                },
            },
        },
        404: { description: 'Equipment not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Equipments'],
});

const equipmentsRoute = new Hono();

equipmentsRoute.get('/', getAllEquipmentsRoute, async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* EquipmentsServiceLive;
        return yield* svc.getAllEquipments();
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

equipmentsRoute.get('/:id', getEquipmentByIdRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* EquipmentsServiceLive;
        return yield* svc.getEquipmentById(id);
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

equipmentsRoute.post('/', createEquipmentRoute, effectValidator('json', CreateEquipmentSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* EquipmentsServiceLive;
        return yield* svc.createEquipment(body);
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

equipmentsRoute.put('/:id', updateEquipmentRoute, effectValidator('json', UpdateEquipmentSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* EquipmentsServiceLive;
        return yield* svc.updateEquipment({ id, ...body });
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

equipmentsRoute.delete('/:id', deleteEquipmentRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* EquipmentsServiceLive;
        return yield* svc.deleteEquipment(id);
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Equipment deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { equipmentsRoute };
