import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import {
  type I18NColumnMapper,
  i18nColumnMapper,
} from '@/migration-scripts/constants';
import { BaseConverter } from '../base-converter';
import type { Mapping, MySqlI18NBase, PgI18NBase } from '../types';

export class PeopleRoleTypeI18nMigrationConverter extends BaseConverter {
  private peopleRoleTypeI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySqlI18NBase,
    peopleRoleTypeIdMappings: Record<string, string>,
  ): PgI18NBase {
    const postgresId = this.generateCuid2();
    // Get the new PostgreSQL ID for the people_role_type
    const newPeopleRoleTypeId =
      peopleRoleTypeIdMappings[mysqlRecord.data_id.toString()];
    if (!newPeopleRoleTypeId) {
      throw new Error(
        `No mapping found for people_role_type_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.peopleRoleTypeI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newPeopleRoleTypeId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for type_fonction_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_fonction_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No type_fonction_trad INSERT statements found.');
        return;
      }

      // Load people_role_type ID mappings
      const peopleRoleTypeIdMappings =
        await this.loadEntityIdMappings('type_fonction');

      const allPostgresRecords: PgI18NBase[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, peopleRoleTypeIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const columns = this.extractColumnNames(insertStatements[0] ?? '').map(
        (column) =>
          (i18nColumnMapper[column as I18NColumnMapper] ?? column) as string,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'people_role_types_i18n',
        'People Role Type I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.peopleRoleTypeI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'type_fonction_trad', postgres: 'people_role_types_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} people_role_types_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
