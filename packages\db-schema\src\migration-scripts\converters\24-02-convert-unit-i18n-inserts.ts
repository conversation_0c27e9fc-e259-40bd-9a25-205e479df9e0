import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySQLOrganizationI18N {
  id: number;
  data_id: number;
  locale: string;
  name: string | null;
  description: string | null;
  otherNames: string | null;
  acronym: string | null;
}

interface PostgresUnitI18N {
  id: string;
  dataId: string;
  locale: string;
  name: string | null;
  description: string | null;
  otherNames: string | null;
  acronym: string | null;
}

export class UnitI18nMigrationConverter extends BaseConverter {
  private unitI18nMappings: Mapping[] = [];

  private parseUnitI18NInsertStatement(
    sqlStatement: string,
  ): MySQLOrganizationI18N[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        name: values.name,
        description: values.description,
        otherNames: values.pseudonyme,
        acronym: values.acronym,
      },
    ];
  }

  private convertToPostgres(
    mysqlRecord: MySQLOrganizationI18N,
    unitIdMappings: Record<string, string>,
    validUnitIds: Set<number>,
  ): PostgresUnitI18N | null {
    // Skip if the data_id is not in our valid list (not a 'unite')
    if (!validUnitIds.has(mysqlRecord.data_id)) {
      return null;
    }

    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the unit
    const newUnitId = unitIdMappings[mysqlRecord.data_id.toString()];

    if (!newUnitId) {
      console.warn(`No mapping found for unit_id: ${mysqlRecord.data_id}`);
      return null;
    }

    // Store mapping for future reference
    this.unitI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      dataId: newUnitId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.name,
      description: mysqlRecord.description,
      otherNames: mysqlRecord.otherNames,
      acronym: mysqlRecord.acronym,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for organization_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'organization_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No organization_trad INSERT statements found.');
        return;
      }

      // Load unit ID mappings
      const unitIdMappings = await this.loadEntityIdMappings('unite');

      // Get the list of valid unit IDs (from the organizations table with table_ref='unite')
      const organizationInsertStatements = this.extractInsertStatements(
        sqlContent,
        'organization',
      );

      const validUnitIds = new Set<number>();
      for (const statement of organizationInsertStatements) {
        const values = this.extractValuesFromInsertStatement(statement);
        if (values.table_ref === 'unite') {
          validUnitIds.add(Number.parseInt(values.organization_id));
        }
      }

      const allPostgresRecords: PostgresUnitI18N[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseUnitI18NInsertStatement(statement);

        for (const record of mysqlRecords) {
          const postgresRecord = this.convertToPostgres(
            record,
            unitIdMappings,
            validUnitIds,
          );

          if (postgresRecord) {
            allPostgresRecords.push(postgresRecord);
          }
        }
      }

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'units_i18n',
        'Unit I18n Inserts',
        [
          'id',
          'data_id',
          'locale',
          'name',
          'description',
          'other_names',
          'acronyms',
        ],
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.unitI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'unite_trad', postgres: 'units_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} units_i18n records (filtered by valid unit IDs)`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
