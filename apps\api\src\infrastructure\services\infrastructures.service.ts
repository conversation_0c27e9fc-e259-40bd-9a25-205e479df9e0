import type {
  CreateInfrastructureSchema,
  UpdateInfrastructureSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';

export class InfrastructuresServiceLive extends Effect.Service<InfrastructuresServiceLive>()(
  'InfrastructuresServiceLive',
  {
    dependencies: [],
    effect: Effect.gen(function* () {
      const getAllInfrastructures = () => {
        return Effect.gen(function* () {
          // Mock data for now - replace with actual repository call
          return [
            {
              id: "infra_001_example_id",
              guidId: "guid_infra_001",
              typeId: "infra_type_001",
              addressId: null,
              statusId: "infra_status_001",
              website: "https://example-infrastructure.com",
              is_featured: true,
              visibilityId: "visibility_001",
              translations: [
                {
                  locale: "en",
                  name: "Advanced Research Infrastructure",
                  description: "State-of-the-art research facility for advanced studies",
                  otherNames: "ARI",
                  acronyms: "ARI"
                },
                {
                  locale: "fr",
                  name: "Infrastructure de Recherche Avancée",
                  description: "Installation de recherche de pointe pour les études avancées",
                  otherNames: "IRA",
                  acronyms: "IRA"
                }
              ],
              createdAt: "2024-01-15T10:00:00Z",
              updatedAt: "2024-01-15T10:00:00Z",
              modifiedBy: null
            }
          ];
        });
      };

      const getInfrastructureById = (id: string) => {
        return Effect.gen(function* () {
          if (id === "infra_001_example_id") {
            return {
              id: "infra_001_example_id",
              guidId: "guid_infra_001",
              typeId: "infra_type_001",
              addressId: null,
              statusId: "infra_status_001",
              website: "https://example-infrastructure.com",
              is_featured: true,
              visibilityId: "visibility_001",
              translations: [
                {
                  locale: "en",
                  name: "Advanced Research Infrastructure",
                  description: "State-of-the-art research facility for advanced studies",
                  otherNames: "ARI",
                  acronyms: "ARI"
                },
                {
                  locale: "fr",
                  name: "Infrastructure de Recherche Avancée",
                  description: "Installation de recherche de pointe pour les études avancées",
                  otherNames: "IRA",
                  acronyms: "IRA"
                }
              ],
              createdAt: "2024-01-15T10:00:00Z",
              updatedAt: "2024-01-15T10:00:00Z",
              modifiedBy: null
            };
          }
          return yield* Effect.fail(
            new Error(`Infrastructure with id ${id} not found`),
          );
        });
      };

      const createInfrastructure = (data: Schema.Schema.Type<typeof CreateInfrastructureSchema>) => {
        return Effect.gen(function* () {
          // Mock creation - return the created infrastructure with generated ID
          return {
            id: "infra_new_" + Date.now(),
            guidId: data.guidId,
            typeId: data.typeId,
            addressId: data.addressId || null,
            statusId: data.statusId,
            website: data.website || null,
            is_featured: data.is_featured || false,
            visibilityId: data.visibilityId,
            translations: data.translations || [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            modifiedBy: null
          };
        });
      };

      const updateInfrastructure = ({ id, ...rest }: { id: string } & Schema.Schema.Type<typeof UpdateInfrastructureSchema>) => {
        return Effect.gen(function* () {
          // Mock update - check if infrastructure exists first
          if (id !== "infra_001_example_id") {
            return yield* Effect.fail(
              new Error(`Infrastructure with id ${id} not found`),
            );
          }

          // Return updated infrastructure
          return {
            id: id,
            guidId: rest.guidId || "guid_infra_001",
            typeId: rest.typeId || "infra_type_001",
            addressId: rest.addressId || null,
            statusId: rest.statusId || "infra_status_001",
            website: rest.website || "https://example-infrastructure.com",
            is_featured: rest.is_featured || true,
            visibilityId: rest.visibilityId || "visibility_001",
            translations: rest.translations || [],
            createdAt: "2024-01-15T10:00:00Z",
            updatedAt: new Date().toISOString(),
            modifiedBy: null
          };
        });
      };

      const deleteInfrastructure = (id: string) => {
        return Effect.gen(function* () {
          if (id !== "infra_001_example_id") {
            return yield* Effect.fail(
              new Error(`Infrastructure with id ${id} not found`),
            );
          }
          // Mock deletion - just return success
          return true;
        });
      };

      return {
        getAllInfrastructures,
        getInfrastructureById,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) { }
