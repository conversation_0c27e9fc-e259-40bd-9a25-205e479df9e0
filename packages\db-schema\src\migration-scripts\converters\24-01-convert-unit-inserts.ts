import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';

interface MySqlOrganization {
  id: number;
  guid_id: number | null;
  table_ref: string;
  type_id: number | null;
  parent_id: number | null;
}

interface PostgresUnit {
  id: string;
  guidId: string;
  typeId: string | null;
  parentId: string | null;
}

export class UnitMigrationConverter extends BaseConverter {
  private unitIdMappings: Record<string, string> = {};
  private unitWithParentMappings: Record<
    string,
    {
      unitPostgresId: string;
      parent: {
        mysqlId: string | null;
        type: 'unit' | 'institution';
        postgresId: string | null;
      };
    }
  > = {};

  private parseInsertStatement(sqlStatement: string): MySqlOrganization[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    // Only include rows where table_ref is 'unite'
    if (values.table_ref !== 'unite') {
      return [];
    }

    return [
      {
        id: Number.parseInt(values.organization_id),
        guid_id:
          values.guid_id === null ? null : Number.parseInt(values.guid_id),
        table_ref: this.parseNullableString(values.table_ref) || '',
        type_id:
          values.type_id === null ? null : Number.parseInt(values.type_id),
        parent_id:
          values.parent_id === null ? null : Number.parseInt(values.parent_id),
      },
    ];
  }

  private handleUnitParentIdMappings(
    mysqlUnit: MySqlOrganization,
    postgresUnit: PostgresUnit,
    institutionIdMappings: Record<string, string>,
  ) {
    if (!mysqlUnit.parent_id) {
      return;
    }

    const postgresParentId =
      this.unitIdMappings[mysqlUnit.parent_id?.toString()] !== undefined
        ? (this.unitIdMappings[mysqlUnit.parent_id?.toString()] ?? null)
        : institutionIdMappings[mysqlUnit.parent_id?.toString()] !== undefined
          ? (institutionIdMappings[mysqlUnit.parent_id?.toString()] ?? null)
          : null;
    const isParentInstitution = Boolean(
      institutionIdMappings[mysqlUnit.parent_id?.toString()] !== undefined,
    );

    this.unitWithParentMappings[mysqlUnit.id.toString()] = {
      unitPostgresId: postgresUnit.id,
      parent: {
        mysqlId: mysqlUnit.parent_id?.toString() || null,
        type: isParentInstitution ? 'institution' : 'unit',
        postgresId: postgresParentId,
      },
    };
  }

  private convertToPostgres(
    mysqlOrganization: MySqlOrganization,
    unitTypeMap: Map<number, number>,
    guidIdMappings: Record<string, string>,
    unitTypeIdMappings: Record<string, string>,
  ): PostgresUnit | null {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the guid
    const newGuidId = mysqlOrganization.guid_id
      ? (guidIdMappings[mysqlOrganization.guid_id.toString()] ?? null)
      : null;
    if (!newGuidId) {
      console.warn(
        `No mapping found for guid_id: ${mysqlOrganization.guid_id}`,
      );
      return null;
    }

    // Get the unit type ID from the unite table
    const typeUniteId = unitTypeMap.get(mysqlOrganization.id);

    // Get the new PostgreSQL ID for the unit type
    let newTypeId = null;
    if (typeUniteId) {
      newTypeId = unitTypeIdMappings[typeUniteId.toString()] ?? null;
      if (!newTypeId) {
        console.warn(`No mapping found for type_unite_id: ${typeUniteId}`);
      }
    }

    this.unitIdMappings[mysqlOrganization.id.toString()] = postgresId;

    return {
      id: postgresId,
      guidId: newGuidId,
      typeId: newTypeId,
      parentId: null,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for organization table (old MySQL table name)
      const organizationInsertStatements = this.extractInsertStatements(
        sqlContent,
        'organization',
      );

      if (organizationInsertStatements.length === 0) {
        console.log('No organization INSERT statements found.');
        return;
      }

      // Extract INSERT statements for unite table to get type_unite_id
      const uniteInsertStatements = this.extractInsertStatements(
        sqlContent,
        'unite',
      );

      if (uniteInsertStatements.length === 0) {
        console.log('No unite INSERT statements found.');
        return;
      }

      // Create a map of organization_id to type_unite_id
      const unitTypeMap = new Map<number, number>();
      for (const statement of uniteInsertStatements) {
        try {
          const values = this.extractValuesFromInsertStatement(statement);
          const organizationId = Number.parseInt(values.organization_id);
          const typeUniteId = Number.parseInt(values.type_unite_id);
          unitTypeMap.set(organizationId, typeUniteId);
        } catch (error) {
          console.warn('Error parsing unite statement:', error);
        }
      }

      // Load guid ID mappings
      const guidIdMappings = await this.loadEntityIdMappings('guid');

      // Load unit type ID mappings (from type_unite to unit_types)
      const unitTypeIdMappings = await this.loadEntityIdMappings('type_unite');
      const institutionIdMappings =
        await this.loadEntityIdMappings('etablissement');

      const mysqlOrganizations: MySqlOrganization[] = [];
      const allPostgresUnits: PostgresUnit[] = [];

      // First pass: collect all units to create parent ID mappings
      for (const statement of organizationInsertStatements) {
        const orgs = this.parseInsertStatement(statement);
        if (orgs.length === 0) {
          continue; // Skip if not 'unite'
        }

        mysqlOrganizations.push(...orgs);
      }

      // Second pass: convert to PostgreSQL units with proper parent references
      for (const org of mysqlOrganizations) {
        const postgresUnit = this.convertToPostgres(
          org,
          unitTypeMap,
          guidIdMappings,
          unitTypeIdMappings,
        );

        if (postgresUnit) {
          allPostgresUnits.push(postgresUnit);
        } else {
          console.log(`Failed to convert unit with id=${org.id}`);
        }
      }

      for (const org of mysqlOrganizations) {
        const postgresUnit = allPostgresUnits.find(
          (unit) => unit.id === this.unitIdMappings[org.id.toString()],
        );
        if (!postgresUnit) {
          console.log(`Failed to find unit with id=${org.id}`);
          continue;
        }

        this.handleUnitParentIdMappings(
          org,
          postgresUnit,
          institutionIdMappings,
        );
      }

      const columns = ['id', 'guid_id', 'type_id', 'parent_id'];

      // Generate output with both inserts and mappings
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresUnits,
        'units',
        'Unit Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'unite', postgres: 'units' },
        this.unitIdMappings,
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'unite_avec_parent', postgres: 'unit_parents' },
        this.unitWithParentMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresUnits.length} units records (filtered by table_ref='unite')`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
