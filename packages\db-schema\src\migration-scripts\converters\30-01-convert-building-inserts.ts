import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping, MySQLBuilding, PostgresBuilding } from '../types';

export class BuildingMigrationConverter extends BaseConverter {
  private buildingMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySQLBuilding[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        sad_id: values.uid,
        campus_id:
          values.campus_id !== 'NULL'
            ? Number.parseInt(values.campus_id)
            : null,
      },
    ];
  }

  private async convertToPostgres(
    mysqlBuilding: MySQLBuilding,
    campusIdMappings: Record<string, string>,
  ): Promise<PostgresBuilding> {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.buildingMappings.push({
      mysqlId: mysqlBuilding.id,
      postgresId: postgresId,
    });

    // Map the campus ID
    let campusId = null;
    if (mysqlBuilding.campus_id !== null) {
      campusId = campusIdMappings[mysqlBuilding.campus_id.toString()] ?? null;
      if (!campusId && mysqlBuilding.campus_id) {
        console.warn(
          `No mapping found for campus_id: ${mysqlBuilding.campus_id}`,
        );
      }
    }

    return {
      id: postgresId,
      campus_id: campusId,
      civic_address_id: null,
      sad_id: mysqlBuilding.sad_id,
      di_id: null,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for batiment table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'batiment',
      );

      if (insertStatements.length === 0) {
        console.log('No batiment INSERT statements found.');
        return;
      }

      const campusIdMappings = await this.loadEntityIdMappings('campus');

      const allPostgresBuildings: PostgresBuilding[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlBuildings = this.parseInsertStatement(statement);
        const postgresBuildings = await Promise.all(
          mysqlBuildings.map((building) =>
            this.convertToPostgres(building, campusIdMappings),
          ),
        );
        allPostgresBuildings.push(...postgresBuildings);
      }

      const columns = [
        'id',
        'campus_id',
        'civic_address_id',
        'sad_id',
        'di_id',
      ];
      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresBuildings,
          'buildings',
          'Building Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.buildingMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'batiment', postgres: 'buildings' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresBuildings.length} buildings records`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
