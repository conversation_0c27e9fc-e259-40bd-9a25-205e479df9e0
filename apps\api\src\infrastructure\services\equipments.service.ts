import { EquipmentsRepositoryLive } from '@/infrastructure/repositories/equipments.repository';
import { EquipmentNotFoundError } from '@rie/domain/errors';
import type {
  CreateEquipmentSchema,
  UpdateEquipmentSchema,
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

export class EquipmentsServiceLive extends Effect.Service<EquipmentsServiceLive>()(
  'EquipmentsServiceLive',
  {
    dependencies: [EquipmentsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const equipmentsRepository = yield* EquipmentsRepositoryLive;

      const getAllEquipments = () => {
        return Effect.gen(function* () {
          return yield* equipmentsRepository.findAllEquipments();
        });
      };

      const getEquipmentById = (id: string) => {
        return Effect.gen(function* () {
          const equipment = yield* equipmentsRepository.findEquipmentById(id);
          if (!equipment) {
            return yield* Effect.fail(
              new EquipmentNotFoundError({ id }),
            );
          }
          return equipment;
        });
      };

      const createEquipment = (data: Schema.Schema.Type<typeof CreateEquipmentSchema>) => {
        return Effect.gen(function* () {
          return yield* equipmentsRepository.createEquipment({
            guidId: data.guidId,
            campusAddressId: data.campusAddressId,
            isCampusAddressConfidential: data.isCampusAddressConfidential,
            model: data.model,
            serialNumber: data.serialNumber,
            homologationNumber: data.homologationNumber,
            inventoryNumber: data.inventoryNumber,
            doi: data.doi,
            useInClinicalTrial: data.useInClinicalTrial,
            isHidden: data.isHidden,
            typeId: data.typeId,
            statusId: data.statusId,
            workingPercentage: data.workingPercentage,
            monetaryCost: data.monetaryCost,
            inKindCost: data.inKindCost,
            manufactureYear: data.manufactureYear,
            acquisitionDate: data.acquisitionDate,
            installationDate: data.installationDate,
            decommissioningDate: data.decommissioningDate,
            scientificManagerId: data.scientificManagerId,
            manufacturerId: data.manufacturerId,
            supplierId: data.supplierId,
            infrastructureId: data.infrastructureId,
            isFeatured: data.isFeatured,
            institutionId: data.institutionId,
            modifiedBy: data.modifiedBy,
          });
        });
      };

      const updateEquipment = (params: { id: string } & Schema.Schema.Type<typeof UpdateEquipmentSchema>) => {
        const { id, ...updateData } = params;
        return Effect.gen(function* () {
          const existingEquipment = yield* equipmentsRepository.findEquipmentById(id);
          if (!existingEquipment) {
            return yield* Effect.fail(
              new EquipmentNotFoundError({ id }),
            );
          }

          // Update the equipment
          const updatedEquipment = yield* equipmentsRepository.updateEquipment({
            id: id,
            guidId: updateData.guidId,
            campusAddressId: updateData.campusAddressId,
            isCampusAddressConfidential: updateData.isCampusAddressConfidential,
            model: updateData.model,
            serialNumber: updateData.serialNumber,
            homologationNumber: updateData.homologationNumber,
            inventoryNumber: updateData.inventoryNumber,
            doi: updateData.doi,
            useInClinicalTrial: updateData.useInClinicalTrial,
            isHidden: updateData.isHidden,
            typeId: updateData.typeId,
            statusId: updateData.statusId,
            workingPercentage: updateData.workingPercentage,
            monetaryCost: updateData.monetaryCost,
            inKindCost: updateData.inKindCost,
            manufactureYear: updateData.manufactureYear,
            acquisitionDate: updateData.acquisitionDate,
            installationDate: updateData.installationDate,
            decommissioningDate: updateData.decommissioningDate,
            scientificManagerId: updateData.scientificManagerId,
            manufacturerId: updateData.manufacturerId,
            supplierId: updateData.supplierId,
            infrastructureId: updateData.infrastructureId,
            isFeatured: updateData.isFeatured,
            institutionId: updateData.institutionId,
            modifiedBy: updateData.modifiedBy,
          });

          // TODO: Add translation update support when needed

          return updatedEquipment;
        });
      };

      const deleteEquipment = (id: string) => {
        return Effect.gen(function* () {
          const existingEquipment = yield* equipmentsRepository.findEquipmentById(id);
          if (!existingEquipment) {
            return yield* Effect.fail(
              new EquipmentNotFoundError({ id }),
            );
          }
          return yield* equipmentsRepository.deleteEquipment(id);
        });
      };

      return {
        getAllEquipments,
        getEquipmentById,
        createEquipment,
        updateEquipment,
        deleteEquipment,
      } as const;
    }),
  },
) { }
