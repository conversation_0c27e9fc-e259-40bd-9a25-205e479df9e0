import * as Schema from 'effect/Schema';

// — Translation schema for equipments
export const EquipmentTranslationSchema = Schema.Struct({
  locale: Schema.String,
  name: Schema.optional(Schema.String),
  description: Schema.optional(Schema.String),
  specification: Schema.optional(Schema.String),
  usageContext: Schema.optional(Schema.String),
  risk: Schema.optional(Schema.String),
  comment: Schema.optional(Schema.String),
});

// — Full Equipment shape (corresponds to equipments in DB)
export const EquipmentSchema = Schema.Struct({
  id: Schema.String, // cuid
  guidId: Schema.optional(Schema.String), // nullable
  campusAddressId: Schema.optional(Schema.String), // nullable
  isCampusAddressConfidential: Schema.optional(Schema.Boolean), // default false
  model: Schema.optional(Schema.String), // nullable
  serialNumber: Schema.optional(Schema.String), // nullable
  homologationNumber: Schema.optional(Schema.String), // nullable
  inventoryNumber: Schema.optional(Schema.String), // nullable
  doi: Schema.optional(Schema.String), // nullable
  useInClinicalTrial: Schema.Boolean, // default false
  isHidden: Schema.Boolean, // default false
  typeId: Schema.String, // required
  statusId: Schema.optional(Schema.String), // nullable
  workingPercentage: Schema.optional(Schema.Number), // nullable
  monetaryCost: Schema.optional(Schema.Number), // nullable
  inKindCost: Schema.optional(Schema.Number), // nullable
  manufactureYear: Schema.optional(Schema.Number), // nullable
  acquisitionDate: Schema.optional(Schema.String), // nullable date string
  installationDate: Schema.optional(Schema.String), // nullable date string
  decommissioningDate: Schema.optional(Schema.String), // nullable date string
  scientificManagerId: Schema.optional(Schema.String), // nullable
  manufacturerId: Schema.optional(Schema.String), // nullable
  supplierId: Schema.optional(Schema.String), // nullable
  infrastructureId: Schema.optional(Schema.String), // nullable
  isFeatured: Schema.optional(Schema.Boolean), // default false
  institutionId: Schema.optional(Schema.String), // nullable
  translations: Schema.Array(EquipmentTranslationSchema), // at least []
  createdAt: Schema.String, // ISO timestamp
  updatedAt: Schema.String, // ISO timestamp
  modifiedBy: Schema.optional(Schema.String), // nullable
});

// — Create Equipment input schema (for POST requests)
export const CreateEquipmentSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  campusAddressId: Schema.optional(Schema.String),
  isCampusAddressConfidential: Schema.optional(Schema.Boolean),
  model: Schema.optional(Schema.String),
  serialNumber: Schema.optional(Schema.String),
  homologationNumber: Schema.optional(Schema.String),
  inventoryNumber: Schema.optional(Schema.String),
  doi: Schema.optional(Schema.String),
  useInClinicalTrial: Schema.optional(Schema.Boolean),
  isHidden: Schema.optional(Schema.Boolean),
  typeId: Schema.String, // required
  statusId: Schema.optional(Schema.String),
  workingPercentage: Schema.optional(Schema.Number),
  monetaryCost: Schema.optional(Schema.Number),
  inKindCost: Schema.optional(Schema.Number),
  manufactureYear: Schema.optional(Schema.Number),
  acquisitionDate: Schema.optional(Schema.String),
  installationDate: Schema.optional(Schema.String),
  decommissioningDate: Schema.optional(Schema.String),
  scientificManagerId: Schema.optional(Schema.String),
  manufacturerId: Schema.optional(Schema.String),
  supplierId: Schema.optional(Schema.String),
  infrastructureId: Schema.optional(Schema.String),
  isFeatured: Schema.optional(Schema.Boolean),
  institutionId: Schema.optional(Schema.String),
  translations: Schema.Array(EquipmentTranslationSchema),
  modifiedBy: Schema.optional(Schema.String),
});

// — Update Equipment input schema (for PUT requests)
export const UpdateEquipmentSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  campusAddressId: Schema.optional(Schema.String),
  isCampusAddressConfidential: Schema.optional(Schema.Boolean),
  model: Schema.optional(Schema.String),
  serialNumber: Schema.optional(Schema.String),
  homologationNumber: Schema.optional(Schema.String),
  inventoryNumber: Schema.optional(Schema.String),
  doi: Schema.optional(Schema.String),
  useInClinicalTrial: Schema.optional(Schema.Boolean),
  isHidden: Schema.optional(Schema.Boolean),
  typeId: Schema.optional(Schema.String),
  statusId: Schema.optional(Schema.String),
  workingPercentage: Schema.optional(Schema.Number),
  monetaryCost: Schema.optional(Schema.Number),
  inKindCost: Schema.optional(Schema.Number),
  manufactureYear: Schema.optional(Schema.Number),
  acquisitionDate: Schema.optional(Schema.String),
  installationDate: Schema.optional(Schema.String),
  decommissioningDate: Schema.optional(Schema.String),
  scientificManagerId: Schema.optional(Schema.String),
  manufacturerId: Schema.optional(Schema.String),
  supplierId: Schema.optional(Schema.String),
  infrastructureId: Schema.optional(Schema.String),
  isFeatured: Schema.optional(Schema.Boolean),
  institutionId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(EquipmentTranslationSchema)),
  modifiedBy: Schema.optional(Schema.String),
});

// — Equipment response schema (what the API returns)
export const EquipmentResponseSchema = EquipmentSchema;

// — Payload types for API
export type CreateEquipmentPayload = Schema.Schema.Type<typeof CreateEquipmentSchema>;

export type UpdateEquipmentPayload = Schema.Schema.Type<typeof UpdateEquipmentSchema> & { id: string };
