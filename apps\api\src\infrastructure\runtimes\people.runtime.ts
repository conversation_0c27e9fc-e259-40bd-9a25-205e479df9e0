import { ConfigLive } from '@/infrastructure/config/config.live';
import { PeopleRepositoryLive } from '@/infrastructure/repositories/people.repository';
import { PeopleServiceLive } from '@/infrastructure/services/people.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const PeopleServicesLayer = Layer.mergeAll(
  PeopleRepositoryLive.Default,
  PeopleServiceLive.Default,
);

export const PeopleRuntime = ManagedRuntime.make(
  Layer.provide(
    PeopleServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
