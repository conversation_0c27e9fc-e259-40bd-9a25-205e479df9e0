import { ConfigLive } from '@/infrastructure/config/config.live';
import { BuildingsRepositoryLive } from '@/infrastructure/repositories/buildings.repository';
import { BuildingsServiceLive } from '@/infrastructure/services/buildings.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const BuildingsServicesLayer = Layer.mergeAll(
  BuildingsRepositoryLive.Default,
  BuildingsServiceLive.Default,
);

export const BuildingsRuntime = ManagedRuntime.make(
  Layer.provide(
    BuildingsServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
