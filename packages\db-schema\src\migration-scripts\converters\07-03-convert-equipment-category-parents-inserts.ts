import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlEquipmentCategoryParent {
  id: number;
  parent_id: number;
  enfant_id: number;
}

interface PostgresEquipmentCategoryParent {
  parentId: string;
  childId: string;
}

export class EquipmentCategoryParentsMigrationConverter extends BaseConverter {
  private equipmentCategoryParentsMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
  ): MySqlEquipmentCategoryParent[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        parent_id: Number.parseInt(values.parent_id),
        enfant_id: Number.parseInt(values.enfant_id),
      },
    ];
  }

  private async convertToPostgres(
    mysqlRecord: MySqlEquipmentCategoryParent,
    equipmentCategoryIdMappings: Record<string, string>,
  ): Promise<PostgresEquipmentCategoryParent | null> {
    // Get the new PostgreSQL IDs for parent and child categories
    const newParentId =
      equipmentCategoryIdMappings[mysqlRecord.parent_id.toString()];
    const newChildId =
      equipmentCategoryIdMappings[mysqlRecord.enfant_id.toString()];

    if (!newParentId) {
      console.warn(`No mapping found for parent_id: ${mysqlRecord.parent_id}`);
      return null;
    }

    if (!newChildId) {
      console.warn(`No mapping found for enfant_id: ${mysqlRecord.enfant_id}`);
      return null;
    }

    // Store mapping for future reference (using the MySQL record ID)
    this.equipmentCategoryParentsMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: `${newParentId}_${newChildId}`, // Composite key representation
    });

    return {
      parentId: newParentId,
      childId: newChildId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for categorie_equipement_parent table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'categorie_equipement_parent',
      );

      if (insertStatements.length === 0) {
        console.log('No categorie_equipement_parent INSERT statements found.');
        return;
      }

      // Load equipment_category ID mappings
      const equipmentCategoryIdMappings = await this.loadEntityIdMappings(
        'categorie_equipement',
      );

      const allPostgresRecords: PostgresEquipmentCategoryParent[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseInsertStatement(statement);

        for (const record of mysqlRecords) {
          const postgresRecord = await this.convertToPostgres(
            record,
            equipmentCategoryIdMappings,
          );
          if (postgresRecord) {
            allPostgresRecords.push(postgresRecord);
          }
        }
      }

      // Generate output
      let output = this.generateCommentHeader(
        'Equipment Category Parents Inserts',
      );

      const values = allPostgresRecords
        .map((record) => {
          return `(${this.formatSqlValue(record.parentId)}, ${this.formatSqlValue(record.childId)})`;
        })
        .join(',\n');

      output += `INSERT INTO "equipment_category_parents" ("parent_id", "child_id") VALUES\n${values};\n\n`;

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, output);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.equipmentCategoryParentsMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'categorie_equipement_parent',
          postgres: 'equipment_category_parents',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} equipment_category_parents records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
