import { ConfigLive } from '@/infrastructure/config/config.live';
import { FundingProjectsRepositoryLive } from '@/infrastructure/repositories/funding-projects.repository';
import { FundingProjectsServiceLive } from '@/infrastructure/services/funding-projects.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const FundingProjectsServicesLayer = Layer.mergeAll(
  FundingProjectsRepositoryLive.Default,
  FundingProjectsServiceLive.Default,
);

export const FundingProjectsRuntime = ManagedRuntime.make(
  Layer.provide(
    FundingProjectsServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
