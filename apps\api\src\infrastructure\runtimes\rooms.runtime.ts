import { ConfigLive } from '@/infrastructure/config/config.live';
import { RoomsRepositoryLive } from '@/infrastructure/repositories/rooms.repository';
import { RoomsServiceLive } from '@/infrastructure/services/rooms.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const RoomsServicesLayer = Layer.mergeAll(
  RoomsRepositoryLive.Default,
  RoomsServiceLive.Default,
);

export const RoomsRuntime = ManagedRuntime.make(
  Layer.provide(
    RoomsServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
