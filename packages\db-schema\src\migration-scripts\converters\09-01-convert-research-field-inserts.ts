import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class ResearchFieldMigrationConverter extends BaseConverter {
  private researchFieldMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlResearchField: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.researchFieldMappings.push({
      mysqlId: mysqlResearchField.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for domaine_recherche table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'domaine_recherche',
      );

      if (insertStatements.length === 0) {
        console.log('No domaine_recherche INSERT statements found.');
        return;
      }

      const allPostgresResearchFields: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlResearchFields = this.parseInsertStatement(statement);
        const postgresResearchFields = mysqlResearchFields.map((rf) =>
          this.convertToPostgres(rf),
        );
        allPostgresResearchFields.push(...postgresResearchFields);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresResearchFields,
          'research_fields',
          'Research Field Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.researchFieldMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'domaine_recherche', postgres: 'research_fields' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresResearchFields.length} research_fields records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
