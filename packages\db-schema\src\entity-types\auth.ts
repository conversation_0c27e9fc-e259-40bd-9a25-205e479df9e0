import type {
  accounts,
  contextTypeEnum,
  domainEnum,
  permissionActionEnum,
  permissionGroupPermissions,
  permissionGroups,
  permissions,
  roleInheritance,
  rolePermissionGroups,
  rolePermissions,
  roles,
  sessions,
  userPermissions,
  userRoles,
  users,
  verifications,
} from '@/schemas';

import type { InferInsertModel, InferSelectModel } from 'drizzle-orm';

// User types
export type DbUser = InferSelectModel<typeof users>;
export type DbUserInput = Omit<InferInsertModel<typeof users>, 'id'>;

// Session types
export type DbSession = InferSelectModel<typeof sessions>;
export type DbSessionInput = InferInsertModel<typeof sessions>;

// Account types
export type DbAccount = InferSelectModel<typeof accounts>;
export type DbAccountInput = InferInsertModel<typeof accounts>;

// Verification types
export type DbVerification = InferSelectModel<typeof verifications>;
export type DbVerificationInput = InferInsertModel<typeof verifications>;

// Permission types
export type DbPermission = InferSelectModel<typeof permissions>;
export type DbPermissionInput = Omit<
  InferInsertModel<typeof permissions>,
  'id'
>;

// Role types
export type DbRole = InferSelectModel<typeof roles>;
export type DbRoleInput = Omit<InferInsertModel<typeof roles>, 'id'>;

// Role Permission types
export type DbRolePermission = InferSelectModel<typeof rolePermissions>;
export type DbRolePermissionInput = InferInsertModel<typeof rolePermissions>;

// User Role types
export type DbUserRole = InferSelectModel<typeof userRoles>;
export type DbUserRoleInput = InferInsertModel<typeof userRoles>;

// User Permission types
export type DbUserPermission = InferSelectModel<typeof userPermissions>;
export type DbUserPermissionInput = InferInsertModel<typeof userPermissions>;

// Role Inheritance types
export type DbRoleInheritance = InferSelectModel<typeof roleInheritance>;
export type DbRoleInheritanceInput = InferInsertModel<typeof roleInheritance>;

// Permission Group types
export type DbPermissionGroup = InferSelectModel<typeof permissionGroups>;
export type DbPermissionGroupInput = Omit<
  InferInsertModel<typeof permissionGroups>,
  'id'
>;

// Permission Group Permission types
export type DbPermissionGroupPermission = InferSelectModel<
  typeof permissionGroupPermissions
>;
export type DbPermissionGroupPermissionInput = InferInsertModel<
  typeof permissionGroupPermissions
>;

// Role Permission Group types
export type DbRolePermissionGroup = InferSelectModel<
  typeof rolePermissionGroups
>;
export type DbRolePermissionGroupInput = InferInsertModel<
  typeof rolePermissionGroups
>;

// Enum types
export type DbContextType = (typeof contextTypeEnum.enumValues)[number];
export type DbPermissionDomain = (typeof domainEnum.enumValues)[number];
export type DbPermissionAction =
  (typeof permissionActionEnum.enumValues)[number];
