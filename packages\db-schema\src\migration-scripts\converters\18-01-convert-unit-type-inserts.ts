import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class UnitTypeMigrationConverter extends BaseConverter {
  private unitTypeMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlUnitType: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.unitTypeMappings.push({
      mysqlId: mysqlUnitType.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for type_unite table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_unite',
      );

      if (insertStatements.length === 0) {
        console.log('No type_unite INSERT statements found.');
        return;
      }

      const allPostgresUnitTypes: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlUnitTypes = this.parseInsertStatement(statement);
        const postgresUnitTypes = mysqlUnitTypes.map((ut) =>
          this.convertToPostgres(ut),
        );
        allPostgresUnitTypes.push(...postgresUnitTypes);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresUnitTypes,
          'unit_types',
          'Unit Type Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.unitTypeMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'type_unite', postgres: 'unit_types' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresUnitTypes.length} unit_types records`);
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
