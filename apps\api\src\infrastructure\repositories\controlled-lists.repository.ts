import { ConfigLive } from '@/infrastructure/config/config.live';
import * as DbSchema from '@rie/db-schema/schemas';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const CONTROLLED_LISTS = {
  applicationSectors: {
    table: DbSchema.applicationSectors,
    i18nTable: DbSchema.applicationSectorsI18N,
    i18nDataIdField: 'dataId',
  },
  documentationCategories: {
    table: DbSchema.documentationCategories,
    i18nTable: DbSchema.documentationCategoriesI18N,
    i18nDataIdField: 'dataId',
  },
  equipmentCategories: {
    table: DbSchema.equipmentCategories,
    i18nTable: DbSchema.equipmentCategoriesI18N,
    i18nDataIdField: 'dataId',
  },
  equipmentStatuses: {
    table: DbSchema.equipmentStatuses,
    i18nTable: DbSchema.equipmentStatusesI18N,
    i18nDataIdField: 'dataId',
  },
  equipmentTypes: {
    table: DbSchema.equipmentTypes,
    i18nTable: DbSchema.equipmentTypesI18N,
    i18nDataIdField: 'dataId',
  },
  excellenceHubs: {
    table: DbSchema.excellenceHubs,
    i18nTable: DbSchema.excellenceHubsI18N,
    i18nDataIdField: 'dataId',
  },
  fundingProjectTypes: {
    table: DbSchema.fundingProjectTypes,
    i18nTable: DbSchema.fundingProjectTypesI18N,
    i18nDataIdField: 'dataId',
  },
  fundingProjectIdentifierTypes: {
    table: DbSchema.fundingProjectIdentifierTypes,
    i18nTable: DbSchema.fundingProjectIdentifierTypesI18N,
    i18nDataIdField: 'dataId',
  },
  innovationLabs: {
    table: DbSchema.innovationLabs,
    i18nTable: DbSchema.innovationLabsI18N,
    i18nDataIdField: 'dataId',
  },
  infrastructureStatuses: {
    table: DbSchema.infrastructureStatuses,
    i18nTable: DbSchema.infrastructureStatusesI18N,
    i18nDataIdField: 'dataId',
  },
  infrastructureTypes: {
    table: DbSchema.infrastructureTypes,
    i18nTable: DbSchema.infrastructureTypesI18N,
    i18nDataIdField: 'dataId',
  },
  institutionTypes: {
    table: DbSchema.institutionTypes,
    i18nTable: DbSchema.institutionTypesI18N,
    i18nDataIdField: 'dataId',
  },
  mediaTypes: {
    table: DbSchema.mediaTypes,
    i18nTable: DbSchema.mediaTypesI18N,
    i18nDataIdField: 'dataId',
  },
  peopleRoleTypes: {
    table: DbSchema.peopleRoleTypes,
    i18nTable: DbSchema.peopleRoleTypesI18N,
    i18nDataIdField: 'dataId',
  },
  roomCategories: {
    table: DbSchema.roomCategories,
    i18nTable: DbSchema.roomCategoriesI18N,
    i18nDataIdField: 'dataId',
  },
  researchFields: {
    table: DbSchema.researchFields,
    i18nTable: DbSchema.researchFieldsI18N,
    i18nDataIdField: 'dataId',
  },
  techniques: {
    table: DbSchema.techniques,
    i18nTable: DbSchema.techniquesI18N,
    i18nDataIdField: 'dataId',
  },
  unitTypes: {
    table: DbSchema.unitTypes,
    i18nTable: DbSchema.unitTypesI18N,
    i18nDataIdField: 'dataId',
  },
  visibilities: {
    table: DbSchema.visibilities,
    i18nTable: DbSchema.visibilitiesI18N,
    i18nDataIdField: 'dataId',
  },
} as const;

type ControlledListMap = typeof CONTROLLED_LISTS;
type ControlledListKey = keyof ControlledListMap;

interface GetEntityDataParams {
  entityKey: ControlledListKey;
  limit: number;
  locale: string;
}

export class ControlledListsRepositoryLive extends Effect.Service<ControlledListsRepositoryLive>()(
  'ControlledListsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      /**
       * Get controlled list data for a single entity
       */
      const getEntityData = dbClient.makeQuery(
        (execute, { entityKey, limit }: GetEntityDataParams) => {
          return execute((client) => {
            const entityConfig = CONTROLLED_LISTS[entityKey];

            const baseQuery = client
              .select({
                id: entityConfig.table.id,
                name: entityConfig.i18nTable.name,
                locale: entityConfig.i18nTable.locale,
              })
              .from(entityConfig.table)
              .leftJoin(
                entityConfig.i18nTable,
                eq(
                  entityConfig.table.id,
                  entityConfig.i18nTable[entityConfig.i18nDataIdField],
                ),
              );

            return limit === -1 ? baseQuery : baseQuery.limit(limit);
          });
        },
      );

      /**
       * Get controlled lists for multiple entities in parallel
       */
      interface GetControlledListsParams
        extends Omit<GetEntityDataParams, 'entityKey'> {
        entityKeys: readonly ControlledListKey[];
      }

      const getControlledLists = ({
        entityKeys,
        limit,
        locale,
      }: GetControlledListsParams) => {
        const queries = entityKeys.map((entityKey) =>
          getEntityData({ entityKey, limit, locale }),
        );

        return Effect.all(queries, { concurrency: 'unbounded' });
      };

      return {
        getEntityData,
        getControlledLists,
      } as const;
    }),
  },
) {}
