import * as fs from 'node:fs/promises';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '@/migration-scripts/types';
import { BaseConverter } from '../base-converter';

export class EquipmentCategoryMigrationConverter extends BaseConverter {
  private equipmentCategoryMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlEquipmentCategory: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.equipmentCategoryMappings.push({
      mysqlId: mysqlEquipmentCategory.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for categorie_equipement table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'categorie_equipement',
      );

      if (insertStatements.length === 0) {
        console.log('No categorie_equipement INSERT statements found.');
        return;
      }

      const allPostgresEquipmentCategories: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlEquipmentCategories = this.parseInsertStatement(statement);
        const postgresEquipmentCategories = mysqlEquipmentCategories.map((ec) =>
          this.convertToPostgres(ec),
        );
        allPostgresEquipmentCategories.push(...postgresEquipmentCategories);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresEquipmentCategories,
          'equipment_categories',
          'Equipment Category Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.equipmentCategoryMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'categorie_equipement', postgres: 'equipment_categories' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresEquipmentCategories.length} equipment_categories records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
