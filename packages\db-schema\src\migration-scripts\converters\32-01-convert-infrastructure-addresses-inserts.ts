import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlInfrastructureReception {
  id: number;
  infrastructure_id: number;
  local_id: number;
}

interface PostgresCampusAddress {
  id: string;
  room_id: string; // Changed from 'room' to 'room_id' to match schema
}

interface PostgresAddress {
  id: string;
  address_type: string;
  campus_address_id: string;
  civic_address_id: null;
}

export class InfrastructureAddressesMigrationConverter extends BaseConverter {
  private campusAddressMappings: Mapping[] = [];
  private addressMappings: Mapping[] = [];
  private roomMappings: Record<string, string> = {};

  private parseInsertStatement(
    sqlStatement: string,
  ): MySqlInfrastructureReception[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        infrastructure_id: Number.parseInt(values.infrastructure_id),
        local_id: Number.parseInt(values.local_id),
      },
    ];
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for infrastructure_reception table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'infrastructure_reception',
      );

      if (insertStatements.length === 0) {
        console.log('No infrastructure_reception INSERT statements found.');
        return;
      }

      // Load infrastructure ID mappings
      const infrastructureIdMappings =
        await this.loadEntityIdMappings('infrastructure');

      // Load room ID mappings (assuming you have these)
      this.roomMappings = await this.loadEntityIdMappings('local');

      console.log('roomMappings', this.roomMappings);

      const campusAddresses: PostgresCampusAddress[] = [];
      const addresses: PostgresAddress[] = [];
      const infrastructureAddressUpdates: {
        infrastructureId: string;
        addressId: string;
      }[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseInsertStatement(statement);

        for (const record of mysqlRecords) {
          // Create campus address
          const campusAddressId = this.generateCuid2();
          const roomId = record.local_id.toString();

          // Get room information - this needs to be the actual room ID from the rooms table
          const roomInfo = this.roomMappings[roomId];

          // Skip if we don't have a valid room mapping
          if (!roomInfo) {
            console.warn(
              `No mapping found for room_id: ${roomId}, skipping record`,
            );
            continue;
          }

          campusAddresses.push({
            id: campusAddressId,
            room_id: roomInfo, // Use the mapped room ID that exists in the rooms table
          });

          this.campusAddressMappings.push({
            mysqlId: record.id,
            postgresId: campusAddressId,
          });

          // Create address
          const addressId = this.generateCuid2();
          addresses.push({
            id: addressId,
            address_type: 'campus',
            campus_address_id: campusAddressId,
            civic_address_id: null,
          });

          this.addressMappings.push({
            mysqlId: record.id,
            postgresId: addressId,
          });

          // Get infrastructure ID
          const infrastructureId =
            infrastructureIdMappings[record.infrastructure_id.toString()];
          if (infrastructureId) {
            infrastructureAddressUpdates.push({
              infrastructureId,
              addressId,
            });
          } else {
            console.warn(
              `No mapping found for infrastructure_id: ${record.infrastructure_id}`,
            );
          }
        }
      }

      const campusAddressInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          campusAddresses,
          'campus_addresses',
          'Campus Addresses Inserts',
          ['id', 'room_id'],
        );

      const addressInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          addresses,
          'addresses',
          'Addresses Inserts',
          ['id', 'address_type', 'campus_address_id', 'civic_address_id'], // Correct column names
        );

      // Generate SQL for infrastructure updates
      let infrastructureAddresses = this.generateCommentHeader(
        'Infrastructure Address Updates',
      );
      for (const update of infrastructureAddressUpdates) {
        infrastructureAddresses += `UPDATE "infrastructures" SET "address_id" = '${update.addressId}' WHERE "id" = '${update.infrastructureId}';\n`;
      }

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, campusAddressInsertsWithMappings);
      await fs.appendFile(outputPath, addressInsertsWithMappings);
      await fs.appendFile(outputPath, infrastructureAddresses);

      // Write mappings to JSON files
      const outputDir = path.dirname(outputPath);
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'infrastructure_reception', postgres: 'campus_addresses' },
        Object.fromEntries(
          this.campusAddressMappings.map((m) => [
            m.mysqlId.toString(),
            m.postgresId,
          ]),
        ),
      );

      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'infrastructure_reception', postgres: 'addresses' },
        Object.fromEntries(
          this.addressMappings.map((m) => [m.mysqlId.toString(), m.postgresId]),
        ),
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Created ${campusAddresses.length} campus_addresses records`,
      );
      console.log(`- Created ${addresses.length} addresses records`);
      console.log(
        `- Updated ${infrastructureAddressUpdates.length} infrastructure records`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
