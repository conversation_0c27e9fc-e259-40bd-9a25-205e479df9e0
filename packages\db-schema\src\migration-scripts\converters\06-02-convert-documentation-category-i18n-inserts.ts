import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class DocumentationCategoryI18nMigrationConverter extends BaseConverter {
  private documentationCategoryI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    documentationCategoryIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the documentation_category
    const newDocumentationCategoryId =
      documentationCategoryIdMappings[mysqlRecord.data_id.toString()];
    if (!newDocumentationCategoryId) {
      throw new Error(
        `No mapping found for documentation_category_id: ${mysqlRecord.data_id}`,
      );
    }

    // Store mapping for future reference
    this.documentationCategoryI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newDocumentationCategoryId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for categorie_documentation_trad table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'categorie_documentation_trad',
      );

      if (insertStatements.length === 0) {
        console.log(
          'No documentation_categories_i18n INSERT statements found.',
        );
        return;
      }

      // Load documentation_category ID mappings
      const documentationCategoryIdMappings = await this.loadEntityIdMappings(
        'categorie_documentation',
      );

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) =>
          this.convertToPostgres(record, documentationCategoryIdMappings),
        );
        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'documentation_categories_i18n',
        'Documentation Category I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.documentationCategoryI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'categorie_documentation_trad',
          postgres: 'documentation_categories_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} documentation_categories_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
