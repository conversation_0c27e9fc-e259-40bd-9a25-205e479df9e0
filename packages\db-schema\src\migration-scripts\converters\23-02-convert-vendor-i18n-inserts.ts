import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySQLOrganizationI18N {
  id: number;
  data_id: number;
  locale: string;
  name: string | null;
  description: string | null;
  otherNames: string | null;
}

interface PostgresVendorI18N {
  id: string;
  dataId: string;
  locale: string;
  name: string | null;
  description: string | null;
  otherNames: string | null;
}

export class VendorI18nMigrationConverter extends BaseConverter {
  private vendorI18nMappings: Mapping[] = [];

  private parseVendorI18NInsertStatement(
    sqlStatement: string,
  ): MySQLOrganizationI18N[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        name: values.name,
        description: values.description,
        otherNames: values.pseudonyme,
      },
    ];
  }

  private async convertToPostgres(
    mysqlRecord: MySQLOrganizationI18N,
    vendorIdMappings: Record<string, string>,
    validVendorIds: Set<number>,
  ): Promise<PostgresVendorI18N | null> {
    // Skip if the data_id is not in our valid list (not a 'fournisseur')
    if (!validVendorIds.has(mysqlRecord.data_id)) {
      return null;
    }

    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the vendor
    const newVendorId = vendorIdMappings[mysqlRecord.data_id.toString()];
    if (!newVendorId) {
      console.warn(`No mapping found for vendor_id: ${mysqlRecord.data_id}`);
      return null;
    }

    // Store mapping for future reference
    this.vendorI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      dataId: newVendorId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.name,
      description: mysqlRecord.description,
      otherNames: mysqlRecord.otherNames,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for organization_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'organization_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No organization_trad INSERT statements found.');
        return;
      }

      // Load vendor ID mappings
      const vendorIdMappings = await this.loadEntityIdMappings('fournisseur');

      // Get the list of valid vendor IDs (from the organizations table with table_ref='fournisseur')
      const organizationInsertStatements = this.extractInsertStatements(
        sqlContent,
        'organization',
      );

      const validVendorIds = new Set<number>();
      for (const statement of organizationInsertStatements) {
        const values = this.extractValuesFromInsertStatement(statement);
        if (values.table_ref === 'fournisseur') {
          validVendorIds.add(Number.parseInt(values.organization_id));
        }
      }

      console.log(`Found ${validVendorIds.size} valid vendor IDs`);

      const allPostgresRecords: PostgresVendorI18N[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseVendorI18NInsertStatement(statement);

        for (const record of mysqlRecords) {
          const postgresRecord = await this.convertToPostgres(
            record,
            vendorIdMappings,
            validVendorIds,
          );

          if (postgresRecord) {
            allPostgresRecords.push(postgresRecord);
          }
        }
      }

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'vendors_i18n',
        'Vendor I18n Inserts',
        ['id', 'data_id', 'locale', 'name', 'description', 'other_names'],
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.vendorI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'fournisseur_trad', postgres: 'vendors_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} vendors_i18n records (filtered by valid vendor IDs)`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
