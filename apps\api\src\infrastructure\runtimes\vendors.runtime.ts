import { ConfigLive } from '@/infrastructure/config/config.live';
import { VendorsRepositoryLive } from '@/infrastructure/repositories/vendors.repository';
import { VendorsServiceLive } from '@/infrastructure/services/vendors.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const VendorServicesLayer = Layer.mergeAll(
  VendorsRepositoryLive.Default,
  VendorsServiceLive.Default,
);

export const VendorsRuntime = ManagedRuntime.make(
  Layer.provide(
    VendorServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
