import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlPerson {
  id: number;
  guid_id: number | null;
  uid: string | null;
  family_name: string;
  given_name: string;
}

interface PostgresPerson {
  id: string;
  guidId: string | null;
  uid: string | null;
  firstName: string;
  lastName: string;
}

export class PersonMigrationConverter extends BaseConverter {
  private personMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlPerson[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.person_id),
        guid_id: values.guid_id ? Number.parseInt(values.guid_id) : null,
        uid: values.uid,
        family_name: values.family_name,
        given_name: values.given_name,
      },
    ];
  }

  private convertToPostgres(
    mysqlPerson: MySqlPerson,
    guidMappings: Record<string, string>,
  ): PostgresPerson {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.personMappings.push({
      mysqlId: mysqlPerson.id,
      postgresId: postgresId,
    });

    const guidId = mysqlPerson.guid_id
      ? (guidMappings[mysqlPerson.guid_id.toString()] ?? null)
      : null;
    if (!guidId) {
      console.warn(`No mapping found for guid_id: ${mysqlPerson.guid_id}`);
    }

    return {
      id: postgresId,
      guidId: guidId,
      uid: mysqlPerson.uid,
      firstName: mysqlPerson.given_name,
      lastName: mysqlPerson.family_name,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for person table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'person',
      );

      if (insertStatements.length === 0) {
        console.log('No person INSERT statements found.');
        return;
      }

      const guidMappings = await this.loadEntityIdMappings('guid');

      const allPostgresPeople: PostgresPerson[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlPeople = this.parseInsertStatement(statement);

        // Process each person record
        for (const mysqlPerson of mysqlPeople) {
          const postgresPerson = await this.convertToPostgres(
            mysqlPerson,
            guidMappings,
          );
          allPostgresPeople.push(postgresPerson);
        }
      }

      // Add the actual INSERT statement
      const columns = ['id', 'guid_id', 'uid', 'first_name', 'last_name'];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresPeople,
          'peoples',
          'People Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.personMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'person', postgres: 'peoples' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresPeople.length} people records`);
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
