import { ConfigLive } from '@/infrastructure/config/config.live';
import { InfrastructuresRepositoryLive } from '@/infrastructure/repositories/infrastructures.repository';
import { InfrastructuresServiceLive } from '@/infrastructure/services/infrastructures.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
    ConfigLive.pipe(
        Effect.map((env) =>
            PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
        ),
    ),
).pipe(Layer.provide(ConfigLive.Default));

const InfrastructuresServicesLayer = Layer.mergeAll(
    InfrastructuresRepositoryLive.Default,
    InfrastructuresServiceLive.Default,
);

export const InfrastructuresRuntime = ManagedRuntime.make(
    Layer.provide(
        InfrastructuresServicesLayer,
        Layer.merge(ConfigLive.Default, PgDatabaseLive),
    ),
);
