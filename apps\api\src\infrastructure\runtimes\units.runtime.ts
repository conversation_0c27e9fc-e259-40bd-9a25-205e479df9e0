import { ConfigLive } from '@/infrastructure/config/config.live';
import { UnitsRepositoryLive } from '@/infrastructure/repositories/units.repository';
import { UnitsServiceLive } from '@/infrastructure/services/units.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const UnitsServicesLayer = Layer.mergeAll(
  UnitsRepositoryLive.Default,
  UnitsServiceLive.Default,
);

export const UnitsRuntime = ManagedRuntime.make(
  Layer.provide(
    UnitsServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
