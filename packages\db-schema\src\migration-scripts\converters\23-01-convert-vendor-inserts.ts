import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlOrganization {
  id: number;
  guid_id: number | null;
  table_ref: string;
}

interface PostgresVendor {
  id: string;
  startDate: string | null;
  endDate: string | null;
}

export class VendorMigrationConverter extends BaseConverter {
  private vendorMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlOrganization[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    // Only include rows where table_ref is 'fournisseur'
    if (values.table_ref !== 'fournisseur') {
      return [];
    }

    return [
      {
        id: Number.parseInt(values.organization_id),
        guid_id:
          values.guid_id === 'NULL' ? null : Number.parseInt(values.guid_id),
        table_ref: this.parseNullableString(values.table_ref) || '',
      },
    ];
  }

  private convertToPostgres(
    mysqlOrganization: MySqlOrganization,
  ): PostgresVendor {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.vendorMappings.push({
      mysqlId: mysqlOrganization.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      startDate: null,
      endDate: null,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for organization table (old MySQL table name)
      const organizationInsertStatements = this.extractInsertStatements(
        sqlContent,
        'organization',
      );

      if (organizationInsertStatements.length === 0) {
        console.log('No organization INSERT statements found.');
        return;
      }

      const mysqlOrganizations: MySqlOrganization[] = [];
      const allPostgresVendors: PostgresVendor[] = [];

      console.log(
        `Processing ${organizationInsertStatements.length} vendors INSERT statements`,
      );

      // Process each INSERT statement
      for (const statement of organizationInsertStatements) {
        const orgs = this.parseInsertStatement(statement);
        if (orgs.length === 0) {
          continue; // Skip if not 'fournisseur'
        }

        mysqlOrganizations.push(...orgs);

        for (const org of orgs) {
          const postgresVendor = this.convertToPostgres(org);
          if (postgresVendor) {
            allPostgresVendors.push(postgresVendor);
          } else {
            console.log(`Failed to convert vendor with id=${org.id}`);
          }
        }
      }

      const columns = ['id', 'start_date', 'end_date'];

      // Generate output with both inserts and mappings
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresVendors,
        'vendors',
        'Vendor Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.vendorMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'fournisseur', postgres: 'vendors' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresVendors.length} vendors records (filtered by table_ref='fournisseur')`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
