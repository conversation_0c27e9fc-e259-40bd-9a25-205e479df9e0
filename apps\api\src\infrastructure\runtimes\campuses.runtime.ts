import { ConfigLive } from '@/infrastructure/config/config.live';
import { CampusesRepositoryLive } from '@/infrastructure/repositories/campuses.repository';
import { CampusesServiceLive } from '@/infrastructure/services/campuses.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      PgDatabase.pgLayer({ url: env.PG_DATABASE_URL, ssl: env.ENV === 'prod' }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const CampusesServicesLayer = Layer.mergeAll(
  CampusesRepositoryLive.Default,
  CampusesServiceLive.Default,
);

export const CampusesRuntime = ManagedRuntime.make(
  Layer.provide(
    CampusesServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
