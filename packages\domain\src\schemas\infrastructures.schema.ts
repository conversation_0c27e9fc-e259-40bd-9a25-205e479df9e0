import * as Schema from 'effect/Schema';

// — Translation schema for infrastructures
export const InfrastructureTranslationSchema = Schema.Struct({
  locale: Schema.String,
  name: Schema.optional(Schema.String),
  description: Schema.optional(Schema.String),
  otherNames: Schema.optional(Schema.String),
  acronyms: Schema.optional(Schema.String),
});

// — Full Infrastructure shape (corresponds to infrastructures in DB)
export const InfrastructureSchema = Schema.Struct({
  id: Schema.String, // cuid
  guidId: Schema.String, // required
  typeId: Schema.String, // required
  addressId: Schema.optional(Schema.String), // nullable
  statusId: Schema.String, // required
  website: Schema.optional(Schema.String), // nullable
  is_featured: Schema.optional(Schema.Boolean), // nullable
  visibilityId: Schema.String, // required
  translations: Schema.Array(InfrastructureTranslationSchema), // at least []
  createdAt: Schema.String, // ISO timestamp
  updatedAt: Schema.String, // ISO timestamp
  modifiedBy: Schema.optional(Schema.String), // nullable
});

// — Create Infrastructure input schema (for POST requests)
export const CreateInfrastructureSchema = Schema.Struct({
  guidId: Schema.String,
  typeId: Schema.String,
  addressId: Schema.optional(Schema.String),
  statusId: Schema.String,
  website: Schema.optional(Schema.String),
  is_featured: Schema.optional(Schema.Boolean),
  visibilityId: Schema.String,
  translations: Schema.Array(InfrastructureTranslationSchema),
});

// — Update Infrastructure input schema (for PUT requests)
export const UpdateInfrastructureSchema = Schema.Struct({
  guidId: Schema.optional(Schema.String),
  typeId: Schema.optional(Schema.String),
  addressId: Schema.optional(Schema.String),
  statusId: Schema.optional(Schema.String),
  website: Schema.optional(Schema.String),
  is_featured: Schema.optional(Schema.Boolean),
  visibilityId: Schema.optional(Schema.String),
  translations: Schema.optional(Schema.Array(InfrastructureTranslationSchema)),
});

// — Infrastructure response schema (what the API returns)
export const InfrastructureResponseSchema = InfrastructureSchema;
