import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class InfrastructureStatusMigrationConverter extends BaseConverter {
  private infrastructureStatusMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlInfrastructureStatus: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.infrastructureStatusMappings.push({
      mysqlId: mysqlInfrastructureStatus.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for statut_infrastructure table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'statut_infrastructure',
      );

      if (insertStatements.length === 0) {
        console.log('No statut_infrastructure INSERT statements found.');
        return;
      }

      const allPostgresInfrastructureStatuses: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlInfrastructureStatuses =
          this.parseInsertStatement(statement);
        const postgresInfrastructureStatuses = mysqlInfrastructureStatuses.map(
          (is) => this.convertToPostgres(is),
        );
        allPostgresInfrastructureStatuses.push(
          ...postgresInfrastructureStatuses,
        );
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresInfrastructureStatuses,
          'infrastructure_statuses',
          'Infrastructure Status Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.infrastructureStatusMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'statut_infrastructure', postgres: 'infrastructure_statuses' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresInfrastructureStatuses.length} infrastructure_statuses records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
