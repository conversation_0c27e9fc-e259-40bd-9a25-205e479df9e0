import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class EquipmentCategoryI18nMigrationConverter extends BaseConverter {
  private equipmentCategoryI18nMappings: Mapping[] = [];

  private parseEquipmentI18NInsertStatement(
    sqlStatement: string,
  ): MySQLI18NDescription[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        nom: values.nom,
        description: values.description,
      },
    ];
  }

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    equipmentCategoryIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the equipment_category
    const newEquipmentCategoryId =
      equipmentCategoryIdMappings[mysqlRecord.data_id.toString()];
    if (!newEquipmentCategoryId) {
      throw new Error(
        `No mapping found for equipment_category_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.equipmentCategoryI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newEquipmentCategoryId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for categorie_equipement_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'categorie_equipement_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No categorie_equipement_trad INSERT statements found.');
        return;
      }

      // Load equipment_category ID mappings
      const equipmentCategoryIdMappings = await this.loadEntityIdMappings(
        'categorie_equipement',
      );

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseEquipmentI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, equipmentCategoryIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      // Generate output
      const postgresInserts = this.generatePostgresI18NInsert(
        allPostgresRecords,
        'equipment_categories_i18n',
        'Equipment Category I18n Inserts',
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.equipmentCategoryI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'categorie_equipement_trad',
          postgres: 'equipment_categories_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} equipment_categories_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
