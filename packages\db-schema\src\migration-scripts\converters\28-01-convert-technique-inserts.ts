import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class TechniqueMigrationConverter extends BaseConverter {
  private techniqueMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlTechnique: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.techniqueMappings.push({
      mysqlId: mysqlTechnique.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for technique table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'technique',
      );

      if (insertStatements.length === 0) {
        console.log('No technique INSERT statements found.');
        return;
      }

      const allPostgresTechniques: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlTechniques = this.parseInsertStatement(statement);
        const postgresTechniques = mysqlTechniques.map((technique) =>
          this.convertToPostgres(technique),
        );
        allPostgresTechniques.push(...postgresTechniques);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresTechniques,
          'techniques',
          'Technique Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.techniqueMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'technique', postgres: 'techniques' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresTechniques.length} techniques records`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
