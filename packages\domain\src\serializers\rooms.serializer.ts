import {
  DbRoomSchema,
  type Room,
  type RoomItem,
  RoomItemSchema,
} from '@/schemas';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const DbRoomToRoomItem = Schema.transformOrFail(
  DbRoomSchema,
  RoomItemSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            value: raw.id,
            label: raw.number || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error ? error.message : 'Failed to parse room',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

export const transformRoomData = (data: Room[]): RoomItem[] => {
  return data.map((room) => ({
    value: room.id,
    label: room.number || room.id,
  }));
};
