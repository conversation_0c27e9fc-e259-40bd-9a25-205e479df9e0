import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InfrastructuresRuntime } from '@/infrastructure/runtimes/infrastructures.runtime';
import { InfrastructuresServiceLive } from '@/infrastructure/services/infrastructures.service';
import { effectValidator } from '@hono/effect-validator';
import {
    CreateInfrastructureSchema,
    UpdateInfrastructureSchema,
    InfrastructureSchema,
    ResourceIdSchema
} from '@rie/domain/schemas';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllInfrastructuresRoute = describeRoute({
    description: 'Get all Infrastructures',
    operationId: 'getAllInfrastructures',
    responses: {
        200: {
            description: 'List of infrastructures',
            content: { 'application/json': { schema: resolver(Schema.Array(InfrastructureSchema)) } },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Infrastructures'],
});

export const getInfrastructureByIdRoute = describeRoute({
    description: 'Get an Infrastructure by ID',
    operationId: 'getInfrastructureById',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Infrastructure ID (CUID format)',
            example: 'infra123abc456def789ghi'
        },
    ],
    responses: {
        200: {
            description: 'Infrastructure found',
            content: { 'application/json': { schema: resolver(InfrastructureSchema) } },
        },
        404: {
            description: 'Infrastructure not found',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ error: Schema.String })),
                },
            },
        },
        500: { description: 'Internal server error' },
    },
    tags: ['Infrastructures'],
});

export const createInfrastructureRoute = describeRoute({
    description: 'Create an Infrastructure',
    operationId: 'createInfrastructure',
    requestBody: {
        required: true,
        content: {
            'application/json': { 
                schema: resolver(CreateInfrastructureSchema),
                example: {
                    guidId: "guid_infra_123",
                    typeId: "infra_type_001",
                    statusId: "infra_status_001",
                    website: "https://example-infrastructure.com",
                    is_featured: false,
                    visibilityId: "visibility_001",
                    translations: [
                        {
                            locale: "en",
                            name: "Advanced Research Infrastructure",
                            description: "State-of-the-art research facility for advanced studies",
                            otherNames: "ARI",
                            acronyms: "ARI"
                        },
                        {
                            locale: "fr",
                            name: "Infrastructure de Recherche Avancée",
                            description: "Installation de recherche de pointe pour les études avancées",
                            otherNames: "IRA",
                            acronyms: "IRA"
                        }
                    ]
                }
            },
        },
    },
    responses: {
        201: {
            description: 'Infrastructure created',
            content: { 'application/json': { schema: resolver(InfrastructureSchema) } },
        },
        400: { description: 'Validation error - Invalid input data or missing required fields' },
        404: { description: 'Foreign key not found - Type, Status, or Visibility does not exist' },
        500: { description: 'Internal server error' },
    },
    tags: ['Infrastructures'],
});

export const updateInfrastructureRoute = describeRoute({
    description: 'Update an Infrastructure',
    operationId: 'updateInfrastructure',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Infrastructure ID (CUID format)',
            example: 'infra123abc456def789ghi'
        },
    ],
    requestBody: {
        required: true,
        content: {
            'application/json': { 
                schema: resolver(UpdateInfrastructureSchema),
                example: {
                    guidId: "guid_infra_updated_123",
                    typeId: "infra_type_002",
                    statusId: "infra_status_002",
                    website: "https://updated-infrastructure.com",
                    is_featured: true,
                    visibilityId: "visibility_002",
                    translations: [
                        {
                            locale: "en",
                            name: "Updated Advanced Research Infrastructure",
                            description: "Updated state-of-the-art research facility",
                            otherNames: "Updated ARI",
                            acronyms: "UARI"
                        },
                        {
                            locale: "fr",
                            name: "Infrastructure de Recherche Avancée Mise à Jour",
                            description: "Installation de recherche de pointe mise à jour",
                            otherNames: "IRA Mise à Jour",
                            acronyms: "IRAMAJ"
                        }
                    ]
                }
            },
        },
    },
    responses: {
        200: {
            description: 'Infrastructure updated',
            content: { 'application/json': { schema: resolver(InfrastructureSchema) } },
        },
        400: { description: 'Validation error - Invalid input data' },
        404: { description: 'Infrastructure not found or Foreign key not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Infrastructures'],
});

export const deleteInfrastructureRoute = describeRoute({
    description: 'Delete an Infrastructure',
    operationId: 'deleteInfrastructure',
    parameters: [
        {
            name: 'id',
            in: 'path',
            required: true,
            schema: resolver(ResourceIdSchema),
            description: 'Infrastructure ID (CUID format)',
            example: 'infra123abc456def789ghi'
        },
    ],
    responses: {
        200: {
            description: 'Infrastructure deleted',
            content: {
                'application/json': {
                    schema: resolver(Schema.Struct({ success: Schema.Boolean, message: Schema.String })),
                },
            },
        },
        404: { description: 'Infrastructure not found' },
        500: { description: 'Internal server error' },
    },
    tags: ['Infrastructures'],
});

const infrastructuresRoute = new Hono();

infrastructuresRoute.get('/', getAllInfrastructuresRoute, async (ctx) => {
    const program = Effect.gen(function* () {
        const svc = yield* InfrastructuresServiceLive;
        return yield* svc.getAllInfrastructures();
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

infrastructuresRoute.get('/:id', getInfrastructureByIdRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* InfrastructuresServiceLive;
        return yield* svc.getInfrastructureById(id);
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

infrastructuresRoute.post('/', createInfrastructureRoute, effectValidator('json', CreateInfrastructureSchema), async (ctx) => {
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* InfrastructuresServiceLive;
        return yield* svc.createInfrastructure(body);
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

infrastructuresRoute.put('/:id', updateInfrastructureRoute, effectValidator('json', UpdateInfrastructureSchema), async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const program = Effect.gen(function* () {
        const svc = yield* InfrastructuresServiceLive;
        return yield* svc.updateInfrastructure({ id, ...body });
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

infrastructuresRoute.delete('/:id', deleteInfrastructureRoute, async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
        const svc = yield* InfrastructuresServiceLive;
        return yield* svc.deleteInfrastructure(id);
    });
    const result = await InfrastructuresRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
        return errorResponse;
    }
    if (Exit.isSuccess(result)) {
        return ctx.json({ success: true, message: 'Infrastructure deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
});

export { infrastructuresRoute };
