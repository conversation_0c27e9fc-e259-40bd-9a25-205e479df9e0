import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlRoomCategory {
  local_id: number;
  categorie_id: number;
}

interface PostgresRoomCategory {
  roomId: string;
  categoryId: string;
}

export class RoomCategoryMigrationConverter extends BaseConverter {
  private roomCategoryMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlRoomCategory[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        local_id: Number.parseInt(values.local_id),
        categorie_id: Number.parseInt(values.categorie_id),
      },
    ];
  }

  private async convertToPostgres(
    mysqlRoomCategory: MySqlRoomCategory,
    roomMappings: Record<string, string>,
    categoryMappings: Record<string, string>,
  ): Promise<PostgresRoomCategory | null> {
    const roomId = roomMappings[mysqlRoomCategory.local_id.toString()];
    if (!roomId) {
      console.warn(
        `No mapping found for room_id: ${mysqlRoomCategory.local_id}`,
      );
      return null;
    }

    // Load category ID mapping
    const categoryId =
      categoryMappings[mysqlRoomCategory.categorie_id.toString()];
    if (!categoryId) {
      console.warn(
        `No mapping found for category_id: ${mysqlRoomCategory.categorie_id}`,
      );
      return null;
    }

    // Store mapping for future reference
    this.roomCategoryMappings.push({
      mysqlId: `${mysqlRoomCategory.local_id}_${mysqlRoomCategory.categorie_id}`,
      postgresId: `${roomId}_${categoryId}`,
    });

    return {
      roomId: roomId,
      categoryId: categoryId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for local_has_categorie table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'local_has_categorie',
      );

      if (insertStatements.length === 0) {
        console.log('No local_has_categorie INSERT statements found.');
        return;
      }

      const allPostgresRoomCategories: PostgresRoomCategory[] = [];
      const roomMappings = await this.loadEntityIdMappings('local');
      const categoryMappings =
        await this.loadEntityIdMappings('categorie_local');
      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRoomCategories = this.parseInsertStatement(statement);

        // Process each room-category record
        for (const mysqlRoomCategory of mysqlRoomCategories) {
          const postgresRoomCategory = await this.convertToPostgres(
            mysqlRoomCategory,
            roomMappings,
            categoryMappings,
          );
          if (postgresRoomCategory) {
            allPostgresRoomCategories.push(postgresRoomCategory);
          }
        }
      }

      // Add the actual INSERT statement
      const columns = ['room_id', 'category_id'];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresRoomCategories,
          'room_associated_categories',
          'Room Associated Categories Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.roomCategoryMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'local_has_categorie',
          postgres: 'room_associated_categories',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRoomCategories.length} room-category associations`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
