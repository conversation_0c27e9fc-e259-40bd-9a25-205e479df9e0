import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import {
  type I18NColumnMapper,
  i18nColumnMapper,
} from '@/migration-scripts/constants';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class ResearchFieldI18nMigrationConverter extends BaseConverter {
  private researchFieldI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    researchFieldIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the research_field
    const newResearchFieldId =
      researchFieldIdMappings[mysqlRecord.data_id.toString()];
    if (!newResearchFieldId) {
      throw new Error(
        `No mapping found for research_field_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.researchFieldI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newResearchFieldId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for domaine_recherche_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'domaine_recherche_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No domaine_recherche_trad INSERT statements found.');
        return;
      }

      // Load research_field ID mappings
      const researchFieldIdMappings =
        await this.loadEntityIdMappings('domaine_recherche');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, researchFieldIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const columns = this.extractColumnNames(insertStatements[0] ?? '').map(
        (column) => i18nColumnMapper[column as I18NColumnMapper] ?? column,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'research_fields_i18n',
        'Research Field I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.researchFieldI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'domaine_recherche_trad', postgres: 'research_fields_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} research_fields_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
