import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';

export class BuildingI18nMigrationConverter extends BaseConverter {
  private buildingI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    buildingIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the building
    const newBuildingId = buildingIdMappings[mysqlRecord.data_id.toString()];
    if (!newBuildingId) {
      throw new Error(
        `No mapping found for building_id: ${mysqlRecord.data_id}`,
      );
    }

    // Store mapping for future reference
    this.buildingI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    // Map the fields from MySQL to PostgreSQL
    // In the buildings_i18n table, we have name, description, and otherNames fields
    return {
      id: postgresId,
      data_id: newBuildingId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for batiment_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'batiment_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No batiment_trad INSERT statements found.');
        return;
      }

      // Load building ID mappings
      const buildingIdMappings = await this.loadEntityIdMappings('batiment');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(statement);
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, buildingIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      // Extract column names from the first INSERT statement and map them
      const columns = ['id', 'data_id', 'locale', 'name', 'description'];

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'buildings_i18n',
        'Building I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(outputPath);
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.buildingI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'batiment_trad',
          postgres: 'buildings_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} buildings_i18n records`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
