import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((env) =>
      Database.pgLayer({
        url: env.PG_DATABASE_URL,
        ssl: env.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class InfrastructuresRepositoryLive extends Effect.Service<InfrastructuresRepositoryLive>()(
  'InfrastructuresRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      const findAllInfrastructures = db.makeQuery((exec) =>
        exec((client) =>
          client.query.infrastructures.findMany({
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              addressId: true,
              statusId: true,
              website: true,
              is_featured: true,
              visibilityId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
            },
          }),
        ),
      );

      const findInfrastructureById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.infrastructures.findFirst({
            where: eq(DBSchema.infrastructures.id, id),
            columns: {
              id: true,
              guidId: true,
              typeId: true,
              addressId: true,
              statusId: true,
              website: true,
              is_featured: true,
              visibilityId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                  acronyms: true,
                },
              },
            },
          }),
        ),
      );

      const createInfrastructure = db.makeQuery(
        (
          exec,
          params: {
            guidId: string;
            typeId: string;
            addressId?: string | null;
            statusId: string;
            website?: string | null;
            is_featured?: boolean;
            visibilityId: string;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.infrastructures)
              .values({
                guidId: params.guidId,
                typeId: params.typeId,
                addressId: params.addressId,
                statusId: params.statusId,
                website: params.website,
                is_featured: params.is_featured,
                visibilityId: params.visibilityId,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.infrastructures.id,
                guidId: DBSchema.infrastructures.guidId,
                typeId: DBSchema.infrastructures.typeId,
                addressId: DBSchema.infrastructures.addressId,
                statusId: DBSchema.infrastructures.statusId,
                website: DBSchema.infrastructures.website,
                is_featured: DBSchema.infrastructures.is_featured,
                visibilityId: DBSchema.infrastructures.visibilityId,
                createdAt: DBSchema.infrastructures.createdAt,
                updatedAt: DBSchema.infrastructures.updatedAt,
                modifiedBy: DBSchema.infrastructures.modifiedBy,
              }),
          ),
      );

      const updateInfrastructure = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            guidId?: string;
            typeId?: string;
            addressId?: string | null;
            statusId?: string;
            website?: string | null;
            is_featured?: boolean;
            visibilityId?: string;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.infrastructures)
              .set({
                guidId: params.guidId,
                typeId: params.typeId,
                addressId: params.addressId,
                statusId: params.statusId,
                website: params.website,
                is_featured: params.is_featured,
                visibilityId: params.visibilityId,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.infrastructures.id, params.id))
              .returning({
                id: DBSchema.infrastructures.id,
                guidId: DBSchema.infrastructures.guidId,
                typeId: DBSchema.infrastructures.typeId,
                addressId: DBSchema.infrastructures.addressId,
                statusId: DBSchema.infrastructures.statusId,
                website: DBSchema.infrastructures.website,
                is_featured: DBSchema.infrastructures.is_featured,
                visibilityId: DBSchema.infrastructures.visibilityId,
                createdAt: DBSchema.infrastructures.createdAt,
                updatedAt: DBSchema.infrastructures.updatedAt,
                modifiedBy: DBSchema.infrastructures.modifiedBy,
              }),
          ),
      );

      const deleteInfrastructure = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.infrastructures)
            .where(eq(DBSchema.infrastructures.id, id))
            .returning({ id: DBSchema.infrastructures.id }),
        ),
      );

      const createInfrastructureWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            infrastructure: {
              guidId: string;
              typeId: string;
              addressId?: string | null;
              statusId: string;
              website?: string | null;
              is_featured?: boolean;
              visibilityId: string;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
              acronyms?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            const [inf] = await client
              .insert(DBSchema.infrastructures)
              .values({
                guidId: params.infrastructure.guidId,
                typeId: params.infrastructure.typeId,
                addressId: params.infrastructure.addressId,
                statusId: params.infrastructure.statusId,
                website: params.infrastructure.website,
                is_featured: params.infrastructure.is_featured,
                visibilityId: params.infrastructure.visibilityId,
                modifiedBy: params.infrastructure.modifiedBy,
              })
              .returning({ id: DBSchema.infrastructures.id });

            if (params.translations.length > 0) {
              const rows = params.translations.map((t) => ({
                dataId: inf.id,
                locale: t.locale,
                name: t.name,
                description: t.description,
                otherNames: t.otherNames,
                acronyms: t.acronyms,
              }));
              await client.insert(DBSchema.infrastructuresI18N).values(rows);
            }

            return inf;
          }),
      );

      const updateInfrastructureTranslations = db.makeQuery(
        (
          exec,
          params: {
            infrastructureId: string;
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              otherNames?: string | null;
              acronyms?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            await client
              .delete(DBSchema.infrastructuresI18N)
              .where(
                eq(
                  DBSchema.infrastructuresI18N.dataId,
                  params.infrastructureId,
                ),
              );

            if (params.translations.length > 0) {
              return await client
                .insert(DBSchema.infrastructuresI18N)
                .values(
                  params.translations.map((t) => ({
                    dataId: params.infrastructureId,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    otherNames: t.otherNames,
                    acronyms: t.acronyms,
                  })),
                )
                .returning({
                  id: DBSchema.infrastructuresI18N.id,
                  dataId: DBSchema.infrastructuresI18N.dataId,
                  locale: DBSchema.infrastructuresI18N.locale,
                  name: DBSchema.infrastructuresI18N.name,
                  description: DBSchema.infrastructuresI18N.description,
                  otherNames: DBSchema.infrastructuresI18N.otherNames,
                  acronyms: DBSchema.infrastructuresI18N.acronyms,
                });
            }
            return [];
          }),
      );

      return {
        findAllInfrastructures,
        findInfrastructureById,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
        createInfrastructureWithTranslations,
        updateInfrastructureTranslations,
      } as const;
    }),
  },
) {}
