import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlEquipment {
  equipement_id: number;
  guid_id: number | null;
  modele: string | null;
  numero_serie: string | null;
  numero_homologation: string | null;
  numero_inventaire: string | null;
  doi: string | null;
  est_essais: number | null;
  est_masquer: number | null;
  type_equipement_id: number | null;
  pourcentage_fonctionnalite: number | null;
  cout_espece: number | null;
  cout_nature: number | null;
  annee_fabrication: number | null;
  date_achat: string | null;
  date_installation: string | null;
  date_decommission: string | null;
  titulaire_id: number | null;
  manufacturier_id: number | null;
  fournisseur_id: number | null;
  juridiction_id: number | null;
  infrastructure_id: number | null;
  mettre_en_vedette: number | null;
}

interface PostgresEquipment {
  id: string;
  guid_id: string | null;
  model: string | null;
  serial_number: string | null;
  campus_address_id: string | null;
  is_campus_address_confidential: boolean;
  homologation_number: string | null;
  inventory_number: string | null;
  doi: string | null;
  use_in_clinical_trial: boolean | null;
  is_hidden: boolean;
  type_id: string | null;
  working_percentage: number | null;
  monetary_cost: number | null;
  in_kind_cost: number | null;
  manufacture_year: number | null;
  acquisition_date: string | null;
  installation_date: string | null;
  decommissioning_date: string | null;
  scientific_manager_id: string | null;
  manufacturer_id: string | null;
  supplier_id: string | null;
  institution_id: string | null;
  infrastructure_id: string | null;
  is_featured: boolean;
}

export class EquipmentMigrationConverter extends BaseConverter {
  private equipmentMappings: Mapping[] = [];
  private guidIdMappings: Record<string, string> = {};
  private equipmentTypeIdMappings: Record<string, string> = {};
  private peopleIdMappings: Record<string, string> = {};
  private institutionIdMappings: Record<string, string> = {};
  private infrastructureIdMappings: Record<string, string> = {};

  private parseInsertStatement(sqlStatement: string): MySqlEquipment[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        equipement_id: Number.parseInt(values.equipement_id),
        guid_id:
          values.guid_id !== null ? Number.parseInt(values.guid_id) : null,
        modele: values.modele,
        numero_serie: values.numero_serie,
        numero_homologation: values.numero_homologation,
        numero_inventaire: values.numero_inventaire,
        doi: values.doi,
        est_essais:
          values.est_essais !== null
            ? Number.parseInt(values.est_essais)
            : null,
        est_masquer:
          values.est_masquer !== null
            ? Number.parseInt(values.est_masquer)
            : null,
        type_equipement_id:
          values.type_equipement_id !== null
            ? Number.parseInt(values.type_equipement_id)
            : null,
        pourcentage_fonctionnalite:
          values.pourcentage_fonctionnalite !== null
            ? Number.parseFloat(values.pourcentage_fonctionnalite)
            : null,
        cout_espece:
          values.cout_espece !== null
            ? Number.parseFloat(values.cout_espece)
            : null,
        cout_nature:
          values.cout_nature !== null
            ? Number.parseFloat(values.cout_nature)
            : null,
        annee_fabrication:
          values.annee_fabrication !== null
            ? Number.parseInt(values.annee_fabrication)
            : null,
        date_achat: values.date_achat,
        date_installation: values.date_installation,
        date_decommission: values.date_decommission,
        titulaire_id:
          values.titulaire_id !== null
            ? Number.parseInt(values.titulaire_id)
            : null,
        manufacturier_id:
          values.manufacturier_id !== null
            ? Number.parseInt(values.manufacturier_id)
            : null,
        fournisseur_id:
          values.fournisseur_id !== null
            ? Number.parseInt(values.fournisseur_id)
            : null,
        juridiction_id:
          values.juridiction_id !== null
            ? Number.parseInt(values.juridiction_id)
            : null,
        infrastructure_id:
          values.infrastructure_id !== null
            ? Number.parseInt(values.infrastructure_id)
            : null,
        mettre_en_vedette:
          values.mettre_en_vedette !== null
            ? Number.parseInt(values.mettre_en_vedette)
            : null,
      },
    ];
  }

  private async loadAllMappings(): Promise<void> {
    try {
      this.guidIdMappings = await this.loadEntityIdMappings('guid');
      this.equipmentTypeIdMappings =
        await this.loadEntityIdMappings('type_equipement');
      this.peopleIdMappings = await this.loadEntityIdMappings('person');
      this.institutionIdMappings =
        await this.loadEntityIdMappings('institution');
      this.infrastructureIdMappings =
        await this.loadEntityIdMappings('infrastructure');
    } catch (error) {
      console.error('Error loading mappings:', error);
      throw error;
    }
  }

  private convertToPostgres(mysqlEquipment: MySqlEquipment): PostgresEquipment {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.equipmentMappings.push({
      mysqlId: mysqlEquipment.equipement_id,
      postgresId: postgresId,
    });

    // Map foreign keys using the loaded mappings
    const guidId =
      mysqlEquipment.guid_id !== null
        ? this.guidIdMappings[mysqlEquipment.guid_id.toString()] || null
        : null;

    const typeId =
      mysqlEquipment.type_equipement_id !== null
        ? this.equipmentTypeIdMappings[
            mysqlEquipment.type_equipement_id.toString()
          ] || null
        : null;

    const scientificManager =
      mysqlEquipment.titulaire_id !== null
        ? this.peopleIdMappings[mysqlEquipment.titulaire_id.toString()] || null
        : null;

    const manufacturerId =
      mysqlEquipment.manufacturier_id !== null
        ? this.peopleIdMappings[mysqlEquipment.manufacturier_id.toString()] ||
          null
        : null;

    const suplierId =
      mysqlEquipment.fournisseur_id !== null
        ? this.peopleIdMappings[mysqlEquipment.fournisseur_id.toString()] ||
          null
        : null;

    const institutionId =
      mysqlEquipment.juridiction_id !== null
        ? this.institutionIdMappings[
            mysqlEquipment.juridiction_id.toString()
          ] || null
        : null;

    const infrastructureId =
      mysqlEquipment.infrastructure_id !== null
        ? this.infrastructureIdMappings[
            mysqlEquipment.infrastructure_id.toString()
          ] || null
        : null;

    return {
      id: postgresId,
      guid_id: guidId,
      model: this.formatSqlValue(mysqlEquipment.modele),
      serial_number: this.formatSqlValue(mysqlEquipment.numero_serie),
      campus_address_id: null,
      is_campus_address_confidential: false,
      homologation_number: this.formatSqlValue(
        mysqlEquipment.numero_homologation,
      ),
      inventory_number: this.formatSqlValue(mysqlEquipment.numero_inventaire),
      doi: this.formatSqlValue(mysqlEquipment.doi),
      use_in_clinical_trial: mysqlEquipment.est_essais === 1,
      is_hidden: mysqlEquipment.est_masquer === 1,
      type_id: typeId,
      working_percentage: mysqlEquipment.pourcentage_fonctionnalite,
      monetary_cost: mysqlEquipment.cout_espece,
      in_kind_cost: mysqlEquipment.cout_nature,
      manufacture_year: mysqlEquipment.annee_fabrication,
      acquisition_date: this.formatDate(mysqlEquipment.date_achat),
      installation_date: this.formatDate(mysqlEquipment.date_installation),
      decommissioning_date: this.formatDate(mysqlEquipment.date_decommission),
      scientific_manager_id: scientificManager,
      manufacturer_id: manufacturerId,
      supplier_id: suplierId,
      institution_id: institutionId,
      infrastructure_id: infrastructureId,
      is_featured: mysqlEquipment.mettre_en_vedette === 1,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Load all required ID mappings
      await this.loadAllMappings();

      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for equipement table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'equipement',
      );

      if (insertStatements.length === 0) {
        console.log('No equipement INSERT statements found.');
        return;
      }

      const allPostgresEquipments: PostgresEquipment[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlEquipments = this.parseInsertStatement(statement);
        const postgresEquipments = mysqlEquipments.map((eq) =>
          this.convertToPostgres(eq),
        );
        allPostgresEquipments.push(...postgresEquipments);
      }

      // Generate output
      let output = this.generateCommentHeader('Equipment Inserts');

      // Add the actual INSERT statement with all columns
      const columns = [
        'id',
        'guid_id',
        'model',
        'serial_number',
        'campus_address_id',
        'is_campus_address_confidential',
        'homologation_number',
        'inventory_number',
        'doi',
        'use_in_clinical_trial',
        'is_hidden',
        'type_id',
        'working_percentage',
        'monetary_cost',
        'in_kind_cost',
        'manufacture_year',
        'acquisition_date',
        'installation_date',
        'decommissioning_date',
        'scientific_manager_id',
        'manufacturer_id',
        'supplier_id',
        'institution_id',
        'infrastructure_id',
        'is_featured',
      ];

      const values = allPostgresEquipments
        .map((equipment) => {
          return `(${this.formatSqlValue(equipment.id)}, 
            ${this.formatSqlValue(equipment.guid_id)}, 
            ${this.formatSqlValue(equipment.model)}, 
            ${this.formatSqlValue(equipment.serial_number)}, 
            ${equipment.campus_address_id}, 
            ${equipment.is_campus_address_confidential},
            ${this.formatSqlValue(equipment.homologation_number)}, 
            ${this.formatSqlValue(equipment.inventory_number)}, 
            ${this.formatSqlValue(equipment.doi)}, 
            ${equipment.use_in_clinical_trial}, 
            ${equipment.is_hidden}, 
            ${this.formatSqlValue(equipment.type_id)}, 
            ${this.formatSqlValue(equipment.working_percentage)}, 
            ${this.formatSqlValue(equipment.monetary_cost)}, 
            ${this.formatSqlValue(equipment.in_kind_cost)}, 
            ${this.formatSqlValue(equipment.manufacture_year)}, 
            ${this.formatSqlValue(equipment.acquisition_date)}, 
            ${this.formatSqlValue(equipment.installation_date)}, 
            ${this.formatSqlValue(equipment.decommissioning_date)}, 
            ${this.formatSqlValue(equipment.scientific_manager_id)}, 
            ${this.formatSqlValue(equipment.manufacturer_id)}, 
            ${this.formatSqlValue(equipment.supplier_id)}, 
            ${this.formatSqlValue(equipment.institution_id)}, 
            ${this.formatSqlValue(equipment.infrastructure_id)}, 
            ${equipment.is_featured})`;
        })
        .join(',\n');

      output += `INSERT INTO "equipments" (${columns.map((c) => `"${c}"`).join(', ')}) VALUES\n${values};\n\n`;

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await fs.appendFile(outputPath, output);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.equipmentMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'equipement', postgres: 'equipments' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresEquipments.length} equipments records`);
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
