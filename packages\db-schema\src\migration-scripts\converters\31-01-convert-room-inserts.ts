import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type { Mapping } from '../types';

interface MySqlRoom {
  id: number;
  numero: string;
  pseudonyme: string | null;
  superficie: number | null;
  portante_plancher: number | null;
  batiment_id: number;
}

interface PostgresRoom {
  id: string;
  number: string;
  area: number | null;
  floorLoad: number | null;
  building_id: string | null;
}

export class RoomMigrationConverter extends BaseConverter {
  private roomMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlRoom[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
        numero: values.numero,
        pseudonyme: values.pseudonyme,
        superficie: values.superficie
          ? Number.parseFloat(values.superficie)
          : null,
        portante_plancher: values.portante_plancher
          ? Number.parseFloat(values.portante_plancher)
          : null,
        batiment_id: Number.parseInt(values.batiment_id),
      },
    ];
  }

  private async convertToPostgres(
    mysqlRoom: MySqlRoom,
    buildingMappings: Record<string, string>,
    jurisdictionMappings: Record<string, string>,
  ): Promise<PostgresRoom> {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.roomMappings.push({
      mysqlId: mysqlRoom.id,
      postgresId: postgresId,
    });

    // Load building ID mappings
    const buildingId =
      buildingMappings[mysqlRoom.batiment_id.toString()] ?? null;

    if (!buildingId) {
      console.warn(
        `No mapping found for building_id: ${mysqlRoom.batiment_id}`,
      );
    }

    return {
      id: postgresId,
      number: mysqlRoom.numero,
      area: mysqlRoom.superficie,
      floorLoad: mysqlRoom.portante_plancher,
      building_id: buildingId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for local table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'local',
      );

      if (insertStatements.length === 0) {
        console.log('No local INSERT statements found.');
        return;
      }

      const allPostgresRooms: PostgresRoom[] = [];
      const jurisdictionMappings =
        await this.loadEntityIdMappings('juridiction');
      const buildingMappings = await this.loadEntityIdMappings('batiment');

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRooms = this.parseInsertStatement(statement);

        // Process each room record
        for (const mysqlRoom of mysqlRooms) {
          const postgresRoom = await this.convertToPostgres(
            mysqlRoom,
            buildingMappings,
            jurisdictionMappings,
          );
          if (postgresRoom) {
            allPostgresRooms.push(postgresRoom);
          }
        }
      }

      // Add the actual INSERT statement
      const columns = ['id', 'number', 'area', 'floor_load', 'building_id'];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresRooms,
          'rooms',
          'Rooms Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.roomMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'local', postgres: 'rooms' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(`- Found ${allPostgresRooms.length} room records`);
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
