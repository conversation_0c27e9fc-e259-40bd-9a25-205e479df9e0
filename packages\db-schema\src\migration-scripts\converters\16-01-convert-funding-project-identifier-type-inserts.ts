import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class FundingProjectIdentifierTypeMigrationConverter extends BaseConverter {
  private fundingProjectIdentifierTypeMappings: Mapping[] = [];

  private parseInsertStatement(sqlStatement: string): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlFundingProjectIdentifierType: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.fundingProjectIdentifierTypeMappings.push({
      mysqlId: mysqlFundingProjectIdentifierType.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for type_numero_identification table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_numero_identification',
      );

      if (insertStatements.length === 0) {
        console.log('No type_numero_identification INSERT statements found.');
        return;
      }

      const allPostgresFundingProjectIdentifierTypes: PostgresI18NBaseReturn[] =
        [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlFundingProjectIdentifierTypes =
          this.parseInsertStatement(statement);
        const postgresFundingProjectIdentifierTypes =
          mysqlFundingProjectIdentifierTypes.map((fpit) =>
            this.convertToPostgres(fpit),
          );
        allPostgresFundingProjectIdentifierTypes.push(
          ...postgresFundingProjectIdentifierTypes,
        );
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresFundingProjectIdentifierTypes,
          'funding_project_identifier_types',
          'Funding Project Identifier Type Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.fundingProjectIdentifierTypeMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'type_numero_identification',
          postgres: 'funding_project_identifier_types',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresFundingProjectIdentifierTypes.length} funding_project_identifier_types records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
