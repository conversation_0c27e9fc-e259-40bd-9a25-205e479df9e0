import * as fs from 'node:fs/promises';
import { BaseConverter } from '../base-converter';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';

export class PersonEmailMigrationConverter extends BaseConverter {
  private personEmailMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
  ): MySqlI18NBaseReturn & { person_id: number; address: string } {
    const values = this.extractValuesFromInsertStatement(sqlStatement);

    return {
      id: Number.parseInt(values.id),
      person_id: Number.parseInt(values.person_id),
      address: values.address,
    };
  }

  private convertToPostgres(
    mysqlPersonEmail: MySqlI18NBaseReturn & {
      person_id: number;
      address: string;
    },
    personIdMappings: Record<string, string>,
  ): PostgresI18NBaseReturn & { person_id: string; address: string } {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the person
    const newPersonId = personIdMappings[mysqlPersonEmail.person_id.toString()];
    if (!newPersonId) {
      throw new Error(
        `No mapping found for person_id: ${mysqlPersonEmail.person_id}`,
      );
    }

    // Store mapping for future reference
    this.personEmailMappings.push({
      mysqlId: mysqlPersonEmail.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      person_id: newPersonId,
      address: mysqlPersonEmail.address,
    };
  }

  async convertFile(inputPath: string, outputPath: string): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(inputPath, 'utf8');

      // Extract INSERT statements for person_email table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'person_email',
      );

      if (insertStatements.length === 0) {
        console.log('No person_email INSERT statements found.');
        return;
      }

      // Load person ID mappings
      const personIdMappings = await this.loadEntityIdMappings('person');

      const allPostgresPersonEmails: (PostgresI18NBaseReturn & {
        person_id: string;
        address: string;
      })[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlPersonEmails = this.parseInsertStatement(statement);
        const postgresPersonEmails = this.convertToPostgres(
          mysqlPersonEmails,
          personIdMappings,
        );
        allPostgresPersonEmails.push(postgresPersonEmails);
      }

      // Generate output with both inserts and mappings
      const columns = ['id', 'person_id', 'address'];
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresPersonEmails,
        'people_associated_emails',
        'People Associated Emails Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
      await fs.mkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await fs.appendFile(outputPath, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.personEmailMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'person_email',
          postgres: 'people_associated_emails',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresPersonEmails.length} people_associated_emails records`,
      );
      console.log(`- PostgreSQL inserts written to: ${outputPath}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
