import { ConfigLive } from '@/infrastructure/config/config.live';
import { ControlledListsRepositoryLive } from '@/infrastructure/repositories/controlled-lists.repository';
import { LocaleRepositoryLive } from '@/infrastructure/repositories/locale.repository';
import { ControlledListsServiceLive } from '@/infrastructure/services/controlled-lists.service';
import { Database as PgDatabase } from '@rie/postgres-db';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

const ControlledListsServicesLayer = Layer.mergeAll(
  ControlledListsRepositoryLive.Default,
  ControlledListsServiceLive.Default,
  LocaleRepositoryLive.Default,
);

export const ControlledListsRuntime = ManagedRuntime.make(
  Layer.provide(
    ControlledListsServicesLayer,
    Layer.merge(ConfigLive.Default, PgDatabaseLive),
  ),
);
