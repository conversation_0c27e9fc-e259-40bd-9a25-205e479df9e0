import { ConfigLive } from '@/infrastructure/config/config.live';
import { DBSchema } from '@rie/db-schema';
import { Database, Database as PgDatabase } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';

const PgDatabaseLive = Layer.unwrapEffect(
  ConfigLive.pipe(
    Effect.map((envVars) =>
      PgDatabase.pgLayer({
        url: envVars.PG_DATABASE_URL,
        ssl: envVars.ENV === 'prod',
      }),
    ),
  ),
).pipe(Layer.provide(ConfigLive.Default));

export class EquipmentsRepositoryLive extends Effect.Service<EquipmentsRepositoryLive>()(
  'EquipmentsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* () {
      const dbClient = yield* Database.PgDatabase;

      const findAllEquipments = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.equipments.findMany({
            columns: {
              id: true,
              guidId: true,
              campusAddressId: true,
              isCampusAddressConfidential: true,
              model: true,
              serialNumber: true,
              homologationNumber: true,
              inventoryNumber: true,
              doi: true,
              useInClinicalTrial: true,
              isHidden: true,
              typeId: true,
              statusId: true,
              workingPercentage: true,
              monetaryCost: true,
              inKindCost: true,
              manufactureYear: true,
              acquisitionDate: true,
              installationDate: true,
              decommissioningDate: true,
              scientificManagerId: true,
              manufacturerId: true,
              supplierId: true,
              infrastructureId: true,
              isFeatured: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  specification: true,
                  usageContext: true,
                  risk: true,
                  comment: true,
                },
              },
            },
          }),
        );
      });

      const findEquipmentById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.equipments.findFirst({
            where: eq(DBSchema.equipments.id, id),
            columns: {
              id: true,
              guidId: true,
              campusAddressId: true,
              isCampusAddressConfidential: true,
              model: true,
              serialNumber: true,
              homologationNumber: true,
              inventoryNumber: true,
              doi: true,
              useInClinicalTrial: true,
              isHidden: true,
              typeId: true,
              statusId: true,
              workingPercentage: true,
              monetaryCost: true,
              inKindCost: true,
              manufactureYear: true,
              acquisitionDate: true,
              installationDate: true,
              decommissioningDate: true,
              scientificManagerId: true,
              manufacturerId: true,
              supplierId: true,
              infrastructureId: true,
              isFeatured: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  specification: true,
                  usageContext: true,
                  risk: true,
                  comment: true,
                },
              },
            },
          }),
        );
      });

      const createEquipment = dbClient.makeQuery(
        (
          execute,
          params: {
            guidId?: string | null;
            campusAddressId?: string | null;
            isCampusAddressConfidential?: boolean;
            model?: string | null;
            serialNumber?: string | null;
            homologationNumber?: string | null;
            inventoryNumber?: string | null;
            doi?: string | null;
            useInClinicalTrial?: boolean;
            isHidden?: boolean;
            typeId: string;
            statusId?: string | null;
            workingPercentage?: number | null;
            monetaryCost?: number | null;
            inKindCost?: number | null;
            manufactureYear?: number | null;
            acquisitionDate?: string | null;
            installationDate?: string | null;
            decommissioningDate?: string | null;
            scientificManagerId?: string | null;
            manufacturerId?: string | null;
            supplierId?: string | null;
            infrastructureId?: string | null;
            isFeatured?: boolean;
            institutionId?: string | null;
            modifiedBy?: string | null;
          },
        ) => {
          return execute((client) => {
            return client
              .insert(DBSchema.equipments)
              .values({
                ...params,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
              })
              .returning({
                id: DBSchema.equipments.id,
                guidId: DBSchema.equipments.guidId,
                campusAddressId: DBSchema.equipments.campusAddressId,
                isCampusAddressConfidential: DBSchema.equipments.isCampusAddressConfidential,
                model: DBSchema.equipments.model,
                serialNumber: DBSchema.equipments.serialNumber,
                homologationNumber: DBSchema.equipments.homologationNumber,
                inventoryNumber: DBSchema.equipments.inventoryNumber,
                doi: DBSchema.equipments.doi,
                useInClinicalTrial: DBSchema.equipments.useInClinicalTrial,
                isHidden: DBSchema.equipments.isHidden,
                typeId: DBSchema.equipments.typeId,
                statusId: DBSchema.equipments.statusId,
                workingPercentage: DBSchema.equipments.workingPercentage,
                monetaryCost: DBSchema.equipments.monetaryCost,
                inKindCost: DBSchema.equipments.inKindCost,
                manufactureYear: DBSchema.equipments.manufactureYear,
                acquisitionDate: DBSchema.equipments.acquisitionDate,
                installationDate: DBSchema.equipments.installationDate,
                decommissioningDate: DBSchema.equipments.decommissioningDate,
                scientificManagerId: DBSchema.equipments.scientificManagerId,
                manufacturerId: DBSchema.equipments.manufacturerId,
                supplierId: DBSchema.equipments.supplierId,
                infrastructureId: DBSchema.equipments.infrastructureId,
                isFeatured: DBSchema.equipments.isFeatured,
                institutionId: DBSchema.equipments.institutionId,
                createdAt: DBSchema.equipments.createdAt,
                updatedAt: DBSchema.equipments.updatedAt,
                modifiedBy: DBSchema.equipments.modifiedBy,
              });
          });
        },
      );

      const updateEquipment = dbClient.makeQuery(
        (
          execute,
          data: {
            id: string;
            guidId?: string | null;
            campusAddressId?: string | null;
            isCampusAddressConfidential?: boolean;
            model?: string | null;
            serialNumber?: string | null;
            homologationNumber?: string | null;
            inventoryNumber?: string | null;
            doi?: string | null;
            useInClinicalTrial?: boolean;
            isHidden?: boolean;
            typeId?: string;
            statusId?: string | null;
            workingPercentage?: number | null;
            monetaryCost?: number | null;
            inKindCost?: number | null;
            manufactureYear?: number | null;
            acquisitionDate?: string | null;
            installationDate?: string | null;
            decommissioningDate?: string | null;
            scientificManagerId?: string | null;
            manufacturerId?: string | null;
            supplierId?: string | null;
            infrastructureId?: string | null;
            isFeatured?: boolean;
            institutionId?: string | null;
            modifiedBy?: string | null;
          },
        ) => {
          return execute((client) =>
            client
              .update(DBSchema.equipments)
              .set({
                guidId: data.guidId,
                campusAddressId: data.campusAddressId,
                isCampusAddressConfidential: data.isCampusAddressConfidential,
                model: data.model,
                serialNumber: data.serialNumber,
                homologationNumber: data.homologationNumber,
                inventoryNumber: data.inventoryNumber,
                doi: data.doi,
                useInClinicalTrial: data.useInClinicalTrial,
                isHidden: data.isHidden,
                typeId: data.typeId,
                statusId: data.statusId,
                workingPercentage: data.workingPercentage,
                monetaryCost: data.monetaryCost,
                inKindCost: data.inKindCost,
                manufactureYear: data.manufactureYear,
                acquisitionDate: data.acquisitionDate,
                installationDate: data.installationDate,
                decommissioningDate: data.decommissioningDate,
                scientificManagerId: data.scientificManagerId,
                manufacturerId: data.manufacturerId,
                supplierId: data.supplierId,
                infrastructureId: data.infrastructureId,
                isFeatured: data.isFeatured,
                institutionId: data.institutionId,
                modifiedBy: data.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.equipments.id, data.id))
              .returning({
                id: DBSchema.equipments.id,
                guidId: DBSchema.equipments.guidId,
                campusAddressId: DBSchema.equipments.campusAddressId,
                isCampusAddressConfidential: DBSchema.equipments.isCampusAddressConfidential,
                model: DBSchema.equipments.model,
                serialNumber: DBSchema.equipments.serialNumber,
                homologationNumber: DBSchema.equipments.homologationNumber,
                inventoryNumber: DBSchema.equipments.inventoryNumber,
                doi: DBSchema.equipments.doi,
                useInClinicalTrial: DBSchema.equipments.useInClinicalTrial,
                isHidden: DBSchema.equipments.isHidden,
                typeId: DBSchema.equipments.typeId,
                statusId: DBSchema.equipments.statusId,
                workingPercentage: DBSchema.equipments.workingPercentage,
                monetaryCost: DBSchema.equipments.monetaryCost,
                inKindCost: DBSchema.equipments.inKindCost,
                manufactureYear: DBSchema.equipments.manufactureYear,
                acquisitionDate: DBSchema.equipments.acquisitionDate,
                installationDate: DBSchema.equipments.installationDate,
                decommissioningDate: DBSchema.equipments.decommissioningDate,
                scientificManagerId: DBSchema.equipments.scientificManagerId,
                manufacturerId: DBSchema.equipments.manufacturerId,
                supplierId: DBSchema.equipments.supplierId,
                infrastructureId: DBSchema.equipments.infrastructureId,
                isFeatured: DBSchema.equipments.isFeatured,
                institutionId: DBSchema.equipments.institutionId,
                createdAt: DBSchema.equipments.createdAt,
                updatedAt: DBSchema.equipments.updatedAt,
                modifiedBy: DBSchema.equipments.modifiedBy,
              }),
          );
        },
      );

      const deleteEquipment = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.equipments)
            .where(eq(DBSchema.equipments.id, id))
            .returning({ id: DBSchema.equipments.id }),
        );
      });

      return {
        findAllEquipments,
        findEquipmentById,
        createEquipment,
        updateEquipment,
        deleteEquipment,
      } as const;
    }),
  },
) { }
