
import * as DBSchema from '@rie/db-schema';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class EquipmentsRepositoryLive extends Effect.Service<EquipmentsRepositoryLive>()(
  'EquipmentsRepositoryLive',
  {
    dependencies: [PgDatabaseLive],
    effect: Effect.gen(function* ($) {
      const db = yield* $(Database.PgDatabase);

      const findAllEquipments = db.makeQuery((exec) =>
        exec((client) =>
          client.query.equipments.findMany({
            columns: {
              id: true,
              guidId: true,
              campusAddressId: true,
              isCampusAddressConfidential: true,
              model: true,
              serialNumber: true,
              homologationNumber: true,
              inventoryNumber: true,
              doi: true,
              useInClinicalTrial: true,
              isHidden: true,
              typeId: true,
              statusId: true,
              workingPercentage: true,
              monetaryCost: true,
              inKindCost: true,
              manufactureYear: true,
              acquisitionDate: true,
              installationDate: true,
              decommissioningDate: true,
              scientificManagerId: true,
              manufacturerId: true,
              supplierId: true,
              infrastructureId: true,
              isFeatured: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  specification: true,
                  usageContext: true,
                  risk: true,
                  comment: true,
                },
              },
            },
          }),
        ),
      );

      const findEquipmentById = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client.query.equipments.findFirst({
            where: eq(DBSchema.equipments.id, id),
            columns: {
              id: true,
              guidId: true,
              campusAddressId: true,
              isCampusAddressConfidential: true,
              model: true,
              serialNumber: true,
              homologationNumber: true,
              inventoryNumber: true,
              doi: true,
              useInClinicalTrial: true,
              isHidden: true,
              typeId: true,
              statusId: true,
              workingPercentage: true,
              monetaryCost: true,
              inKindCost: true,
              manufactureYear: true,
              acquisitionDate: true,
              installationDate: true,
              decommissioningDate: true,
              scientificManagerId: true,
              manufacturerId: true,
              supplierId: true,
              infrastructureId: true,
              isFeatured: true,
              institutionId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  dataId: true,
                  locale: true,
                  name: true,
                  description: true,
                  specification: true,
                  usageContext: true,
                  risk: true,
                  comment: true,
                },
              },
            },
          }),
        ),
      );

      const createEquipment = db.makeQuery(
        (
          exec,
          params: {
            guidId?: string | null;
            campusAddressId?: string | null;
            isCampusAddressConfidential?: boolean;
            model?: string | null;
            serialNumber?: string | null;
            homologationNumber?: string | null;
            inventoryNumber?: string | null;
            doi?: string | null;
            useInClinicalTrial?: boolean;
            isHidden?: boolean;
            typeId: string;
            statusId?: string | null;
            workingPercentage?: number | null;
            monetaryCost?: number | null;
            inKindCost?: number | null;
            manufactureYear?: number | null;
            acquisitionDate?: string | null;
            installationDate?: string | null;
            decommissioningDate?: string | null;
            scientificManagerId?: string | null;
            manufacturerId?: string | null;
            supplierId?: string | null;
            infrastructureId?: string | null;
            isFeatured?: boolean;
            institutionId?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .insert(DBSchema.equipments)
              .values({
                guidId: params.guidId,
                campusAddressId: params.campusAddressId,
                isCampusAddressConfidential: params.isCampusAddressConfidential,
                model: params.model,
                serialNumber: params.serialNumber,
                homologationNumber: params.homologationNumber,
                inventoryNumber: params.inventoryNumber,
                doi: params.doi,
                useInClinicalTrial: params.useInClinicalTrial,
                isHidden: params.isHidden,
                typeId: params.typeId,
                statusId: params.statusId,
                workingPercentage: params.workingPercentage,
                monetaryCost: params.monetaryCost,
                inKindCost: params.inKindCost,
                manufactureYear: params.manufactureYear,
                acquisitionDate: params.acquisitionDate,
                installationDate: params.installationDate,
                decommissioningDate: params.decommissioningDate,
                scientificManagerId: params.scientificManagerId,
                manufacturerId: params.manufacturerId,
                supplierId: params.supplierId,
                infrastructureId: params.infrastructureId,
                isFeatured: params.isFeatured,
                institutionId: params.institutionId,
                modifiedBy: params.modifiedBy,
              })
              .returning({
                id: DBSchema.equipments.id,
                guidId: DBSchema.equipments.guidId,
                campusAddressId: DBSchema.equipments.campusAddressId,
                isCampusAddressConfidential: DBSchema.equipments.isCampusAddressConfidential,
                model: DBSchema.equipments.model,
                serialNumber: DBSchema.equipments.serialNumber,
                homologationNumber: DBSchema.equipments.homologationNumber,
                inventoryNumber: DBSchema.equipments.inventoryNumber,
                doi: DBSchema.equipments.doi,
                useInClinicalTrial: DBSchema.equipments.useInClinicalTrial,
                isHidden: DBSchema.equipments.isHidden,
                typeId: DBSchema.equipments.typeId,
                statusId: DBSchema.equipments.statusId,
                workingPercentage: DBSchema.equipments.workingPercentage,
                monetaryCost: DBSchema.equipments.monetaryCost,
                inKindCost: DBSchema.equipments.inKindCost,
                manufactureYear: DBSchema.equipments.manufactureYear,
                acquisitionDate: DBSchema.equipments.acquisitionDate,
                installationDate: DBSchema.equipments.installationDate,
                decommissioningDate: DBSchema.equipments.decommissioningDate,
                scientificManagerId: DBSchema.equipments.scientificManagerId,
                manufacturerId: DBSchema.equipments.manufacturerId,
                supplierId: DBSchema.equipments.supplierId,
                infrastructureId: DBSchema.equipments.infrastructureId,
                isFeatured: DBSchema.equipments.isFeatured,
                institutionId: DBSchema.equipments.institutionId,
                createdAt: DBSchema.equipments.createdAt,
                updatedAt: DBSchema.equipments.updatedAt,
                modifiedBy: DBSchema.equipments.modifiedBy,
              }),
          ),
      );

      const updateEquipment = db.makeQuery(
        (
          exec,
          params: {
            id: string;
            guidId?: string | null;
            campusAddressId?: string | null;
            isCampusAddressConfidential?: boolean;
            model?: string | null;
            serialNumber?: string | null;
            homologationNumber?: string | null;
            inventoryNumber?: string | null;
            doi?: string | null;
            useInClinicalTrial?: boolean;
            isHidden?: boolean;
            typeId?: string;
            statusId?: string | null;
            workingPercentage?: number | null;
            monetaryCost?: number | null;
            inKindCost?: number | null;
            manufactureYear?: number | null;
            acquisitionDate?: string | null;
            installationDate?: string | null;
            decommissioningDate?: string | null;
            scientificManagerId?: string | null;
            manufacturerId?: string | null;
            supplierId?: string | null;
            infrastructureId?: string | null;
            isFeatured?: boolean;
            institutionId?: string | null;
            modifiedBy?: string | null;
          },
        ) =>
          exec((client) =>
            client
              .update(DBSchema.equipments)
              .set({
                guidId: params.guidId,
                campusAddressId: params.campusAddressId,
                isCampusAddressConfidential: params.isCampusAddressConfidential,
                model: params.model,
                serialNumber: params.serialNumber,
                homologationNumber: params.homologationNumber,
                inventoryNumber: params.inventoryNumber,
                doi: params.doi,
                useInClinicalTrial: params.useInClinicalTrial,
                isHidden: params.isHidden,
                typeId: params.typeId,
                statusId: params.statusId,
                workingPercentage: params.workingPercentage,
                monetaryCost: params.monetaryCost,
                inKindCost: params.inKindCost,
                manufactureYear: params.manufactureYear,
                acquisitionDate: params.acquisitionDate,
                installationDate: params.installationDate,
                decommissioningDate: params.decommissioningDate,
                scientificManagerId: params.scientificManagerId,
                manufacturerId: params.manufacturerId,
                supplierId: params.supplierId,
                infrastructureId: params.infrastructureId,
                isFeatured: params.isFeatured,
                institutionId: params.institutionId,
                modifiedBy: params.modifiedBy,
                updatedAt: new Date().toISOString(),
              })
              .where(eq(DBSchema.equipments.id, params.id))
              .returning({
                id: DBSchema.equipments.id,
                guidId: DBSchema.equipments.guidId,
                campusAddressId: DBSchema.equipments.campusAddressId,
                isCampusAddressConfidential: DBSchema.equipments.isCampusAddressConfidential,
                model: DBSchema.equipments.model,
                serialNumber: DBSchema.equipments.serialNumber,
                homologationNumber: DBSchema.equipments.homologationNumber,
                inventoryNumber: DBSchema.equipments.inventoryNumber,
                doi: DBSchema.equipments.doi,
                useInClinicalTrial: DBSchema.equipments.useInClinicalTrial,
                isHidden: DBSchema.equipments.isHidden,
                typeId: DBSchema.equipments.typeId,
                statusId: DBSchema.equipments.statusId,
                workingPercentage: DBSchema.equipments.workingPercentage,
                monetaryCost: DBSchema.equipments.monetaryCost,
                inKindCost: DBSchema.equipments.inKindCost,
                manufactureYear: DBSchema.equipments.manufactureYear,
                acquisitionDate: DBSchema.equipments.acquisitionDate,
                installationDate: DBSchema.equipments.installationDate,
                decommissioningDate: DBSchema.equipments.decommissioningDate,
                scientificManagerId: DBSchema.equipments.scientificManagerId,
                manufacturerId: DBSchema.equipments.manufacturerId,
                supplierId: DBSchema.equipments.supplierId,
                infrastructureId: DBSchema.equipments.infrastructureId,
                isFeatured: DBSchema.equipments.isFeatured,
                institutionId: DBSchema.equipments.institutionId,
                createdAt: DBSchema.equipments.createdAt,
                updatedAt: DBSchema.equipments.updatedAt,
                modifiedBy: DBSchema.equipments.modifiedBy,
              }),
          ),
      );

      const deleteEquipment = db.makeQuery((exec, id: string) =>
        exec((client) =>
          client
            .delete(DBSchema.equipments)
            .where(eq(DBSchema.equipments.id, id))
            .returning({ id: DBSchema.equipments.id }),
        ),
      );

      const createEquipmentWithTranslations = db.makeQuery(
        (
          exec,
          params: {
            equipment: {
              guidId?: string | null;
              campusAddressId?: string | null;
              isCampusAddressConfidential?: boolean;
              model?: string | null;
              serialNumber?: string | null;
              homologationNumber?: string | null;
              inventoryNumber?: string | null;
              doi?: string | null;
              useInClinicalTrial?: boolean;
              isHidden?: boolean;
              typeId: string;
              statusId?: string | null;
              workingPercentage?: number | null;
              monetaryCost?: number | null;
              inKindCost?: number | null;
              manufactureYear?: number | null;
              acquisitionDate?: string | null;
              installationDate?: string | null;
              decommissioningDate?: string | null;
              scientificManagerId?: string | null;
              manufacturerId?: string | null;
              supplierId?: string | null;
              infrastructureId?: string | null;
              isFeatured?: boolean;
              institutionId?: string | null;
              modifiedBy?: string | null;
            };
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              specification?: string | null;
              usageContext?: string | null;
              risk?: string | null;
              comment?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            const [equipment] = await client
              .insert(DBSchema.equipments)
              .values({
                guidId: params.equipment.guidId,
                campusAddressId: params.equipment.campusAddressId,
                isCampusAddressConfidential: params.equipment.isCampusAddressConfidential,
                model: params.equipment.model,
                serialNumber: params.equipment.serialNumber,
                homologationNumber: params.equipment.homologationNumber,
                inventoryNumber: params.equipment.inventoryNumber,
                doi: params.equipment.doi,
                useInClinicalTrial: params.equipment.useInClinicalTrial,
                isHidden: params.equipment.isHidden,
                typeId: params.equipment.typeId,
                statusId: params.equipment.statusId,
                workingPercentage: params.equipment.workingPercentage,
                monetaryCost: params.equipment.monetaryCost,
                inKindCost: params.equipment.inKindCost,
                manufactureYear: params.equipment.manufactureYear,
                acquisitionDate: params.equipment.acquisitionDate,
                installationDate: params.equipment.installationDate,
                decommissioningDate: params.equipment.decommissioningDate,
                scientificManagerId: params.equipment.scientificManagerId,
                manufacturerId: params.equipment.manufacturerId,
                supplierId: params.equipment.supplierId,
                infrastructureId: params.equipment.infrastructureId,
                isFeatured: params.equipment.isFeatured,
                institutionId: params.equipment.institutionId,
                modifiedBy: params.equipment.modifiedBy,
              })
              .returning({ id: DBSchema.equipments.id });

            if (params.translations.length > 0) {
              const rows = params.translations.map((t) => ({
                dataId: equipment.id,
                locale: t.locale,
                name: t.name,
                description: t.description,
                specification: t.specification,
                usageContext: t.usageContext,
                risk: t.risk,
                comment: t.comment,
              }));
              await client.insert(DBSchema.equipmentsI18N).values(rows);
            }

            return equipment;
          }),
      );

      const updateEquipmentTranslations = db.makeQuery(
        (
          exec,
          params: {
            equipmentId: string;
            translations: Array<{
              locale: string;
              name?: string | null;
              description?: string | null;
              specification?: string | null;
              usageContext?: string | null;
              risk?: string | null;
              comment?: string | null;
            }>;
          },
        ) =>
          exec(async (client) => {
            await client
              .delete(DBSchema.equipmentsI18N)
              .where(
                eq(
                  DBSchema.equipmentsI18N.dataId,
                  params.equipmentId,
                ),
              );

            if (params.translations.length > 0) {
              return await client
                .insert(DBSchema.equipmentsI18N)
                .values(
                  params.translations.map((t) => ({
                    dataId: params.equipmentId,
                    locale: t.locale,
                    name: t.name,
                    description: t.description,
                    specification: t.specification,
                    usageContext: t.usageContext,
                    risk: t.risk,
                    comment: t.comment,
                  })),
                )
                .returning({
                  id: DBSchema.equipmentsI18N.id,
                  dataId: DBSchema.equipmentsI18N.dataId,
                  locale: DBSchema.equipmentsI18N.locale,
                  name: DBSchema.equipmentsI18N.name,
                  description: DBSchema.equipmentsI18N.description,
                  specification: DBSchema.equipmentsI18N.specification,
                  usageContext: DBSchema.equipmentsI18N.usageContext,
                  risk: DBSchema.equipmentsI18N.risk,
                  comment: DBSchema.equipmentsI18N.comment,
                });
            }
            return [];
          }),
      );

      return {
        findAllEquipments,
        findEquipmentById,
        createEquipment,
        updateEquipment,
        deleteEquipment,
        createEquipmentWithTranslations,
        updateEquipmentTranslations,
      } as const;
    }),
  },
) { }
